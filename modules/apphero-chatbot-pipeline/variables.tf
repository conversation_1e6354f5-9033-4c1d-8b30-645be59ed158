variable "environment" {
  description = "Environment name (dev, prod, etc.)"
  type        = string
}

variable "brand" {
  description = "Name of the brand"
  type = string
}

variable accountId {
  description = "Aws account id"
  type        = string  
}

variable "region" {
  description = "region"
  type = string
}

variable "repositoryname" {
  description = "repository name"
  type = string
}

variable "projectname" {
  description = "codebuild project name"
  type = string
}

variable "environment_tag" {
  description = "environment_tag"
  type = string
}

variable "teamname" {
  description = "teamname"
  type = string
}