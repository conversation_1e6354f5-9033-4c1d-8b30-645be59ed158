resource "aws_glue_catalog_database" "apphero_logs_catalog_database" {
  name = "apphero-logs-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_glue_catalog_table" "apphero_logs_catalog_table" {
  name          = "apphero-logs-${var.environment}"
  database_name = aws_glue_catalog_database.apphero_logs_catalog_database.name
  table_type    = var.aws_glue_catalog_table_type

  parameters = {
    EXTERNAL              = "TRUE"
    "parquet.compression" = "SNAPPY"
  }

  storage_descriptor {
    location      = var.storage_descriptor_location
    input_format  = var.storage_descriptor_input_format
    output_format = var.storage_descriptor_output_format

    ser_de_info {
      name                  = var.ser_de_info_name
      serialization_library = var.ser_de_info_serialization_library
      parameters = {
        "serialization.format" = 1
      }
    }

    columns {
      name = "correlationId"
      type = "string"
    }

    columns {
      name = "timestamp"
      type = "string"
    }

    columns {
      name = "type"
      type = "string"
    }

    columns {
      name = "component"
      type = "string"
    }

    columns {
      name = "source"
      type = "string"
    }

    columns {
      name = "destination"
      type = "string"
    }

    columns {
      name = "event"
      type = "string"
    }

    columns {
      name = "usecase"
      type = "string"
    }

    columns {
      name = "sourcePayload"
      type = "string"
    }

    columns {
      name = "destinationPayload"
      type = "string"
    }

    columns {
      name = "logMessage"
      type = "string"
    }

    columns {
      name = "errorMessage"
      type = "string"
    }

    columns {
      name = "brand"
      type = "string"
    }

    columns {
      name = "secondaryKey"
      type = "string"
    }

    columns {
      name = "entityKeyField"
      type = "string"
    }

    columns {
      name = "entityKey"
      type = "string"
    }

    columns {
      name = "destinationObjectType"
      type = "string"
    }

    columns {
      name = "destinationObjectId"
      type = "string"
    }

    columns {
      name = "sourceObjectType"
      type = "string"
    }

    columns {
      name = "sourceObjectId"
      type = "string"
    }

    columns {
        name = "destinationResponse"
        type = "string"
    }

    columns {
      name = "version"
      type = "string"
    }
  }
}

