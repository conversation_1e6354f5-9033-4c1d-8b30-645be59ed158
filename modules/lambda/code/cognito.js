const axios = require("axios");
const AWS = require("aws-sdk");
const ses = new AWS.SES();
const fs = require("fs");
const sts = new AWS.STS();
const { HttpRequest } = require("@aws-sdk/protocol-http");
const { SignatureV4 } = require("@aws-sdk/signature-v4");
const crypto = require("@aws-crypto/sha256-js");
const { Sha256 } = crypto;
const dynamoDB = new AWS.DynamoDB.DocumentClient();
module.exports.preSignUp = async (event, context) => {
  try {
    let user_email = event["request"]["userAttributes"]["email"];
    console.log("user_email", event["request"]["userAttributes"]);
    console.log("Salesforce update starts...", event);

    const headers = {
      "x-api-key": process.env.APIGGATEWAY_API_KEY,
    };

    // Store unconfirmed user in DynamoDB
    const oneHourLater = new Date(new Date().getTime() + 60 * 60 * 1000);
    //delete unconfirmed user
    const isUserExist = await axios.post(
      `${process.env.GUS_APPHERO_API}/unauth/apphero/userexist`,
      { email: user_email, source: event["triggerSource"] },
      { headers }
    );
    const userStatus = isUserExist.data;
    console.log("userStatus", userStatus);
    if (userStatus?.exists && userStatus?.reason === 'CONFIRMED_USER') {
      return event
    }

    if (userStatus?.exists) {
      throw new Error("Account already exist");
    }

    console.log("Updating apphero consent starts...");

    const path = `${process.env.GUS_APPHERO_API}/apphero/updateAppheroConsent`;
    const updateData = {
      email: user_email,
      consent: true,
    };

    await axios.patch(path, updateData, { headers });

    console.log("Updating apphero consent ends...");

    await dynamoDB
      .put({
        TableName: process.env.APPHERO_NOTIFICATION_TABLE,
        Item: {
          PK: "UNCONFIRMED_USER",
          SK: user_email,
          scheduledAt: oneHourLater.toISOString(),
          status: "Unconfirmed",
          createdAt: new Date().toISOString(),
          userAttributes: event.request.userAttributes,
        },
      })
      .promise();

    console.log("Salesforce update ends...");
    console.log("Event:", event);

    return event;
  } catch (error) {
    console.error("Error:", error);
    throw new Error(
      error.response?.data?.message || error || "An unknown error occurred."
    );
  }
};

async function getAppSyncCredentialsByRole(roleArn) {
  const sessionName = `Session-${Date.now()}`;
  const param = {
    RoleArn: roleArn,
    RoleSessionName: sessionName,
  };

  const data = await new Promise((resolve, reject) => {
    sts.assumeRole(param, (err, data) => {
      if (err) reject(err);
      else resolve(data);
    });
  });

  const credentials = data["Credentials"];
  return {
    accessKeyId: credentials.AccessKeyId,
    secretAccessKey: credentials.SecretAccessKey,
    sessionToken: credentials.SessionToken,
  };
}
async function postData(query) {
  try {
    const requestBody = {
      query: query,
    };

    const url = new URL(process.env.GUS_APPHERO_GRAPHQL_API);
    const request = new HttpRequest({
      hostname: url.hostname,
      path: url.pathname,
      body: JSON.stringify(requestBody),
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        host: url.hostname,
      },
    });

    const signer = new SignatureV4({
      credentials: await getAppSyncCredentialsByRole(
        `arn:aws:iam::${process.env.accountId}:role/apphero-appsync-graphQL-${process.env.environment}`
      ),
      service: "appsync",
      region: process.env.region,
      sha256: Sha256,
    });

    const { headers, body, method } = await signer.sign(request);

    const result = await fetch(process.env.GUS_APPHERO_GRAPHQL_API, {
      headers,
      body,
      method,
    }).then((res) => res.json());

    console.log("result", result);
    return result;
  } catch (error) {
    console.error("Error fetching data", error);
  }
}

module.exports.customEmail = async (event, context) => {
  console.log(JSON.stringify(event));

  if (event.triggerSource === "CustomMessage_ForgotPassword") {
    console.log({ event });
    const { userAttributes } = event.request;
    const codeParameter = event.request.codeParameter;
    const firstName = userAttributes["custom:first_name"] || "";
    const lastName = userAttributes["custom:last_name"] || "";
    const userName = `${firstName} ${lastName}`;
    let emailHtmlContent = fs.readFileSync(
      "./forgotPasswordEmailTemplate.html",
      "utf-8"
    );
    emailHtmlContent = emailHtmlContent
      .replace("{{Recipient.FirstName}}", firstName)
      .replace("{{ResetCode}}", codeParameter)
      .replace("{{mailerImg}}", process.env.FORGET_PASSWORD_MAILER_IMG)
      .replace("{{mailerLogo}}", process.env.MAILER_LOGO)
      .replace("{{appheroUrl}}", process.env.APPHERO_URL);
    event.response.emailMessage = emailHtmlContent;
    event.response.emailSubject = "Reset your AppHero password";
    console.log("event", JSON.stringify(event));
    context.done(null, event);
  } else if (event.triggerSource === "CustomMessage_SignUp") {
    const { userAttributes } = event.request;
    const firstName = userAttributes["custom:first_name"] || "";
    const codeParameter = event.request.codeParameter;

    console.log("First Name:", firstName);
    console.log("Code Parameter:", codeParameter);
    console.log("AppHero URL:", process.env.APPHERO_URL);
    let emailHtmlContent = fs.readFileSync(
      "./verifyEmailAdressTemplate.html",
      "utf-8"
    );

    emailHtmlContent = emailHtmlContent
      .replace("{{Recipient.FirstName}}", firstName)
      .replace("{{ResetCode}}", codeParameter)
      .replace("{{appheroUrl}}", process.env.APPHERO_URL);

    console.log("Verify Your Email Address:", emailHtmlContent);

    event.response.emailMessage = emailHtmlContent;
    event.response.emailSubject = "Your Email Verification Code for AppHero";
    context.done(null, event);
  }
};

module.exports.postSignUp = async (event) => {
  console.log("postSignUp", event);
  if (event.triggerSource === "PostConfirmation_ConfirmSignUp") {
    const { userAttributes } = event.request;
    const { email, phone_number } = userAttributes;
    const getCustomAttribute = (key) => userAttributes[`custom:${key}`] || null;
    const first_name = getCustomAttribute("first_name");

    try {
      await dynamoDB
        .delete({
          TableName: process.env.APPHERO_NOTIFICATION_TABLE,
          Key: {
            PK: "UNCONFIRMED_USER",
            SK: email,
          },
        })
        .promise();
      console.log(`Deleted unconfirmed user record for ${email}`);
    } catch (error) {
      console.error("Error deleting unconfirmed user record:", error);
    }

    const userDetails = {
      firstName: first_name,
      lastName: getCustomAttribute("last_name"),
      country: getCustomAttribute("country"),
      countryCode: getCustomAttribute("country_code"),
      phoneNumber: getCustomAttribute("mobile_number"),
      email: email,
      userId: event.userName,
    };
    let graphQLQuery = `mutation {
    createNotification(input: {email:"{{email}}",messageDetails:{messageId:"{{messageId}}",personAccountName: "{{personAccountName}}"},event:WELCOME_STUDENT}) {
        email
        message
        readStatus
        type
        createdAt
        messageDetails {
          messageId
          opportunityId
          taskId
        }
        event
      }
  }`;
    graphQLQuery = graphQLQuery
      .replace("{{email}}", email)
      .replace("{{messageId}}", event.userName)
      .replace("{{personAccountName}}", first_name);
    try {
      const [response, createUserProfile] = await Promise.all([
        postData(graphQLQuery),
        axios.post(
          `${process.env.GUS_APPHERO_API}/unauth/apphero/profile`,
          userDetails
        ),
      ]);
      console.log("GraphQL response:", response);
      console.log("Create User Profile response:", createUserProfile.data);
      return event;
    } catch (err) {
      console.error("Error during API calls:", err);
      return event;
    }
  }
  return event;
};
