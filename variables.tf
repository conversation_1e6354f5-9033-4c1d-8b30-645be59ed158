variable region {
  description = "Region"
  type        = string
}

variable environment {
  description = "Environment"
  type        = string  
}

variable environment_tag {
  description = "Environment tag"
  type        = string  
}

variable accountId {
  description = "Aws account id"
  type        = string  
}


//IAM 
variable s3_role_arn {
  description = "S3 role arn"
  type        = string  
}

//codebuild
variable "gus_apphero_frontend_project_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_source_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_artifact_type" {
  description = ""
  type        = string
}
variable "gus_student_detail_oaf_project_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_source_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_artifact_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_source_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_artifact_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_build_timeout" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_source_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_image" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_artifact_type" {
  description = ""
  type        = string
}


//codepipeline
variable "gus_student_detail_oaf_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_project_name" {
  description = ""
  type        = string
}


variable "gus_student_detail_backend_pipeline_name" {
  description = ""
  type        = string
}


variable "gus_student_detail_backend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_project_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_name" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_project_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_project_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_project_name" {
  description = ""
  type        = string
}


#cognito
variable "cognito_apphero_custom_domain" {
  description = ""
  type = string
}
variable "cognito_linkedin_identity_provider_client_id" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_client_secret" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_oidc_issuer" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_authorize_scopes" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_attributes_request_method" {
  description = ""
  type        = string
}

variable "cognito_user_pool_client_allowed_oauth_flows" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_allowed_oauth_scopes" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_explicit_auth_flows" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_callback_urls" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_logout_urls" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_supported_identity_providers" {
  description = ""
  type        = list(string)
}

variable "ses_mailer_arn" {
  description = ""
  type        = string
}


#apigateway
variable "cognito_user_pool_id" {
  description = ""
  type        = string
}

variable "api_gateway_certificate_acm_certificate_arn" {
  type = string
}

variable "apphero_gateway_custom_domain" {
  type = string
}

variable "gus_studentdetail_gateway_custom_domain" {
  type = string
}

#cloudfront
variable "s3_distribution_http_port" {
  description = ""
  type        = number
}

variable "s3_distribution_https_port" {
  description = ""
  type        = number
}

variable "s3_distribution_origin_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_origin_ssl_protocols" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_enabled" {
  description = ""
  type        = bool
}

variable "apphero_alternative_domain" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_is_ipv6_enabled" {
  description = ""
  type        = bool
}

variable "s3_distribution_default_cache_behavior_allowed_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_default_cache_behavior_cached_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_default_cache_behavior_cache_policy_id" {
  description = ""
  type        = string
}

variable "s3_distribution_default_cache_behavior_response_headers_policy_id" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_geo_restriction_restriction_type" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_certificate_cloudfront_default_certificate" {
  description = ""
  type        = bool
}

variable "s3_distribution_viewer_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}

variable "s3_distribution_student_detail_security_policy"{
  description = ""
  type        = string
}

variable "student_detail_alternative_domain"{
  description = ""
  type        = list(string)
}


#lambda
variable "gus_middleware_api" {
  description = ""
  type        = string
}

variable "gus_apphero_api" {
  description = ""
  type        = string
}

variable "api_id" {
  description = ""
  type        = string
}

variable "apphero_consumer_api_key" {
  description = ""
  type        = string
}

variable "ses_mailer"{
  description = ""
  type        = string
}

variable "mailer_logo"{
  description = ""
  type        = string
}

variable "gus_apphero_graphQl_key" {
  description = ""
  type        = string
}

variable "gus_apphero_graphQl_api" {
  description = ""
  type        = string
}

variable "forget_password_mailer_img"{
  description = ""
  type        = string
}

variable "apphero_login_url"{
  description = ""
  type        = string
}

//glue
variable "aws_glue_catalog_table_type" {
  type        = string
}

variable "storage_descriptor_location" {
  type        = string
}

variable "storage_descriptor_input_format" {
  type        = string
}

variable "storage_descriptor_output_format" {
  type        = string
}

variable "ser_de_info_name" {
  type        = string
}

variable "ser_de_info_serialization_library" {
  type        = string
}

variable "apphero_notification_table"{
  description = ""
  type        = string
}


//athena
variable  "apphero_workgroup_name" {
  type        = string
}

//ses

variable "apphero_email_identity"{
  type = string
}

# chatbot codebuild & codepipeline
variable "brand" {
  description = "Name of the brand"
  type = string
}

variable "repositoryname" {
  description = "repository name"
  type = string
}

variable "projectname" {
  description = "codebuild project name"
  type = string
}

variable "teamname" {
  description = "teamname"
  type = string
}