
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/code"
  output_path = "${path.module}/code-${replace(timestamp(), "/[^0-9]/", "")}.zip"
}

resource "aws_lambda_layer_version" "apphero_eip_infra_layer" {
	  layer_name          = "apphero-infra-layer-${var.environment}"
	  filename            =  "${path.root}/modules/layers/layers.zip"
	  compatible_runtimes = ["nodejs18.x"]

}
resource "aws_lambda_layer_version" "apphero_eip_infra_layer2" {
	  layer_name          = "apphero-infra-layer2-${var.environment}"
	  filename            =  "${path.root}/modules/layers2/layers2.zip"
	  compatible_runtimes = ["nodejs18.x"]

}

resource "aws_lambda_function" "apphero_preSignUpTrigger" {
  function_name = "apphero-preSignUpTrigger-${var.environment}"
  handler       = "cognito.preSignUp"
  runtime       = "nodejs18.x"
  role          = "arn:aws:iam::${var.accountId}:role/apphero-lambda-exec-role-${var.environment}" //gus-lambda-exec-role-${var.environment}
  filename      = data.archive_file.lambda_zip.output_path
  memory_size   = 300
  layers        = [aws_lambda_layer_version.apphero_eip_infra_layer.arn, aws_lambda_layer_version.apphero_eip_infra_layer2.arn]
  timeout       = 30
  depends_on = [
    aws_cloudwatch_log_group.apphero_preSignUpTrigger_log,
  ]
  environment {
    variables = {
        APIGGATEWAY_API_ID: var.api_id
        region: var.region 
        environment: var.environment
        APIGGATEWAY_API_KEY: var.apphero_consumer_api_key
        GUS_MIDDLEWARE_API: var.gus_middleware_api
        GUS_APPHERO_API: var.gus_apphero_api
        APPHERO_NOTIFICATION_TABLE: var.apphero_notification_table
      }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_log_group" "apphero_preSignUpTrigger_log" {
  name              = "apphero-preSignUpTrigger-${var.environment}" 
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_lambda_permission" "allow_userpool_presignup_trigger" {
  statement_id  = "AllowExecutionFromUserpool"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.apphero_preSignUpTrigger.function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = "arn:aws:cognito-idp:eu-west-1:${var.accountId}:userpool/${var.cognito_user_pool_id}"
}

resource "aws_lambda_function" "apphero_postSignUpTrigger" {
  function_name = "apphero-postSignUpTrigger-${var.environment}"
  handler       = "cognito.postSignUp"
  runtime       = "nodejs18.x"
  role          = "arn:aws:iam::${var.accountId}:role/apphero-lambda-exec-role-${var.environment}" //gus-lambda-exec-role-${var.environment}
  filename      = data.archive_file.lambda_zip.output_path
  memory_size   = 300
  layers        = [aws_lambda_layer_version.apphero_eip_infra_layer.arn, aws_lambda_layer_version.apphero_eip_infra_layer2.arn]
  timeout       = 30
  environment {
    variables = {
        APIGGATEWAY_API_ID: var.api_id
        region: var.region 
        environment: var.environment
        accountId: var.accountId
        APIGGATEWAY_API_KEY: var.apphero_consumer_api_key
        GUS_MIDDLEWARE_API: var.gus_middleware_api
        SES_MAILER: var.ses_mailer
        APPHERO_URL: var.apphero_login_url
        MAILER_LOGO: var.mailer_logo
        GUS_APPHERO_API_KEY: var.gus_apphero_graphQl_key
        GUS_APPHERO_GRAPHQL_API: var.gus_apphero_graphQl_api
        GUS_APPHERO_API: var.gus_apphero_api
        APPHERO_NOTIFICATION_TABLE: var.apphero_notification_table
      }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_lambda_permission" "allow_userpool_postsignup_trigger" {
  statement_id  = "AllowExecutionFromUserpool"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.apphero_postSignUpTrigger.function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = "arn:aws:cognito-idp:eu-west-1:${var.accountId}:userpool/${var.cognito_user_pool_id}"
}

resource "aws_lambda_function" "apphero_customEmailTrigger" {
  function_name = "apphero-customEmailTrigger-${var.environment}"
  handler       = "cognito.customEmail"
  runtime       = "nodejs18.x"
  role          = "arn:aws:iam::${var.accountId}:role/apphero-lambda-exec-role-${var.environment}"
  filename      = data.archive_file.lambda_zip.output_path
  memory_size   = 300
  layers        = [aws_lambda_layer_version.apphero_eip_infra_layer.arn, aws_lambda_layer_version.apphero_eip_infra_layer2.arn]
  timeout       = 30
  environment {
    variables = {
        APIGGATEWAY_API_ID: var.api_id
        region: var.region 
        environment: var.environment
        APIGGATEWAY_API_KEY: var.apphero_consumer_api_key
        FORGET_PASSWORD_MAILER_IMG: var.forget_password_mailer_img
        APPHERO_URL: var.apphero_login_url
        MAILER_LOGO: var.mailer_logo
      }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_lambda_permission" "cognito_invoke_custom_email" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = "arn:aws:lambda:${var.region}:${var.accountId}:function:apphero-customEmailTrigger-${var.environment}"
  principal     = "cognito-idp.amazonaws.com"
}