variable region {
  description = "Region"
  type        = string
}

variable environment {
  description = "Environment"
  type        = string  
}

variable environment_tag {
  description = "Environment tag"
  type        = string  
}


variable "aws_glue_catalog_table_type" {
  type        = string
}

variable "storage_descriptor_location" {
  type        = string
}

variable "storage_descriptor_input_format" {
  type        = string
}

variable "storage_descriptor_output_format" {
  type        = string
}

variable "ser_de_info_name" {
  type        = string
}

variable "ser_de_info_serialization_library" {
  type        = string
}