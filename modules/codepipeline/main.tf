//gus-student-detail-oaf-frontend
resource "aws_codepipeline" "gus_student_detail_oaf_pipeline" {
  name     = "${var.gus_student_detail_oaf_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-access-${var.environment}"
  artifact_store {
    location = "${var.gus_student_detail_oaf_pipeline_artifact_store_location}-${var.environment}"
    type     = var.gus_student_detail_oaf_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.gus_student_detail_oaf_pipeline_source_config_repository_name
        BranchName           = var.gus_student_detail_oaf_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.gus_student_detail_oaf_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_rule" "gus_student_detail_oaf_codecommit_trigger_rule" {
  name = "gusStudentDetailCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-student-detail-oaf-frontend"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_target" "gus_student_detail_oaf_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus_student_detail_oaf_codecommit_trigger_rule.name
  target_id = "gusStudentDetailCodecommitEventTarget"
  arn       = aws_codepipeline.gus_student_detail_oaf_pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-start-access-${var.environment}"
}

// gus-student-detail-backend-service 
resource "aws_codepipeline" "gus-student-detail-backend-pipeline" {
  name     = "${var.gus_student_detail_backend_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-access-${var.environment}"
  artifact_store {
    location = "${var.gus_student_detail_backend_pipeline_artifact_store_location}-${var.environment}"
    type     = var.gus_student_detail_backend_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.gus_student_detail_backend_pipeline_source_config_repository_name
        BranchName           = var.gus_student_detail_backend_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.gus_student_detail_backend_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_rule" "gus_student_detail_backend_codecommit_trigger_rule" {
  name = "gusStudentDetailBackendCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-student-detail-backend-service"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_target" "gus_student_detail_backend_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus_student_detail_backend_codecommit_trigger_rule.name
  target_id = "gusStudentDetailBackendCodecommitEventTarget"
  arn       = aws_codepipeline.gus-student-detail-backend-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-start-access-${var.environment}"
}

//gus-apphero
resource "aws_codepipeline" "gus-apphero-pipeline" {
  name     = "${var.gus_apphero_frontend_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-access-${var.environment}"
  artifact_store {
    location = "${var.gus_apphero_frontend_pipeline_artifact_store_location}-${var.environment}"
    type     = var.gus_apphero_frontend_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.gus_apphero_frontend_pipeline_source_config_repository_name
        BranchName           = var.gus_apphero_frontend_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.gus_apphero_frontend_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "gus_apphero_codecommit_trigger_rule" {
  name = "gusAppHeroCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-apphero-frontend"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "gus_apphero_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus_apphero_codecommit_trigger_rule.name
  target_id = "gusAppHeroCodecommitEventTarget"
  arn       = aws_codepipeline.gus-apphero-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-start-access-${var.environment}"
}


// appHero-backend
resource "aws_codepipeline" "apphero_backend_pipeline" {
  name     = "${var.apphero_backend_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-access-${var.environment}"
  artifact_store {
    location = "${var.apphero_backend_pipeline_artifact_store_location}-${var.environment}"
    type     = var.apphero_backend_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.apphero_backend_pipeline_source_config_repository_name
        BranchName           = var.apphero_backend_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.apphero_backend_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_rule" "apphero_backend_codecommit_trigger_rule" {
  name = "appHeroCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:apphero-backend-service"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_target" "apphero_backend_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.apphero_backend_codecommit_trigger_rule.name
  target_id = "appHeroBackendCodecommitEventTarget"
  arn       = aws_codepipeline.apphero_backend_pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-start-access-${var.environment}"
}

//apphero-sf-sync-service
resource "aws_codepipeline" "apphero-sf-sync-service-pipeline" {
  name     = "${var.apphero_sf_sync_service_pipeline_name}-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-access-${var.environment}"
  artifact_store {
    location = "${var.apphero_sf_sync_service_pipeline_artifact_store_location}-${var.environment}"
    type     = var.apphero_sf_sync_service_pipeline_artifact_store_type
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = var.apphero_sf_sync_service_pipeline_source_config_repository_name
        BranchName           = var.apphero_sf_sync_service_pipeline_source_config_branch_name
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "${var.apphero_sf_sync_service_pipeline_project_name}-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "apphero_sf_sync_service_codecommit_trigger_rule" {
  name = "appHeroSfSyncServiceCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:apphero-sf-sync-service"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "apphero_sf_sync_service_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.apphero_sf_sync_service_codecommit_trigger_rule.name
  target_id = "appHeroSfSyncServiceCodecommitEventTarget"
  arn       = aws_codepipeline.apphero-sf-sync-service-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/apphero-codepipeline-start-access-${var.environment}"
}