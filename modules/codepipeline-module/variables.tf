variable "environment" {
  description = "Environment name (dev, prod, etc.)"
  type        = string
}

variable "brand" {
  description = "Brand name"
  type        = string
}

variable "accountId" {
  description = "AWS Account ID"
  type        = string
}

variable "environment_tag" {
  description = "Environment Tag"
  type        = string
}

variable "projectname" {
  description = "Name Of The Project"
  type        = string
}

variable "teamname" {
  description = "Name Of The Team"
  type        = string
}

variable "region" {
  description = "AWS Region"
  type        = string
}

variable "repositoryname" {
  description = "CodeCommit Repo Name"
  type        = string
}

variable "aws_cloudwatch_event_rule" {
  description = "Cloudwatch Event Rule Name"
  type        = string
}

variable "service" {
  description = "aws service where we are deploying (example lambda/ecs)"
  type        = string
}