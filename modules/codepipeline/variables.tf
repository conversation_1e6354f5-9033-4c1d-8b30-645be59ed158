variable region {
  description = "Region"
  type        = string
}

variable environment {
  description = "Environment"
  type        = string  
}

variable environment_tag {
  description = "Environment tag"
  type        = string  
}

variable accountId {
  description = "Aws account id"
  type        = string  
}

//gus_student_detail_oaf_pipeline
variable "gus_student_detail_oaf_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_pipeline_project_name" {
  description = ""
  type        = string
}

//gus student detail backend
variable "gus_student_detail_backend_pipeline_name" {
  description = ""
  type        = string
}


variable "gus_student_detail_backend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_pipeline_project_name" {
  description = ""
  type        = string
}

//gus apphero frontend pipeline
variable "gus_apphero_frontend_pipeline_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_pipeline_project_name" {
  description = ""
  type        = string
}

//apphero backend service
variable "apphero_backend_pipeline_name" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "apphero_backend_pipeline_project_name" {
  description = ""
  type        = string
}

//apphero-sf-sync-service
variable "apphero_sf_sync_service_pipeline_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_artifact_store_location" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_artifact_store_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_pipeline_project_name" {
  description = ""
  type        = string
}
