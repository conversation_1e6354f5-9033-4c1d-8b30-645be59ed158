resource "aws_athena_workgroup" "apphero_athena_workgroup" {
  name = var.apphero_workgroup_name

  configuration {
    enforce_workgroup_configuration    = true
    publish_cloudwatch_metrics_enabled = true

    result_configuration {
      output_location = "s3://gus-athena-query-result-${var.environment}/apphero/"
    }
  }

  tags = {
    Environment = var.environment_tag
    Project     = "APPHERO"
    Team        = "EIP Development Team"
  }
}
