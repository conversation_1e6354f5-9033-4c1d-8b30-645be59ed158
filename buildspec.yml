version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 16
  pre_build:
    commands:
      - curl -LO "https://releases.hashicorp.com/terraform/1.5.1/terraform_1.5.1_linux_amd64.zip"
      - unzip "terraform_1.5.1_linux_amd64.zip"
      - mv terraform /usr/local/bin/
      - rm "terraform_1.5.1_linux_amd64.zip"
      - terraform --version
      - echo "Install Node dependencies for Lambda code"
      - cd modules/layers/nodejs
      - npm install
      - cd ../../../
      - cd modules/layers2/nodejs
      - npm install
      - cd ../../..
  build:
    commands:
      - echo "Build phase"
      - echo "Initialize Terraform"
      - terraform init -backend-config=backend-${stage}.hcl
      - |
        if terraform workspace list | grep -q ${stage}; then
          echo "Workspace ${stage} exists, selecting it"
          echo "Hello"
          terraform workspace select ${stage}
        else
          echo "Workspace ${stage} does not exist, creating it"
          terraform workspace new ${stage}
        fi
      - echo "Terraform validate"
      - terraform validate
      - echo "Terraform plan"
      - terraform plan -lock=false -var-file=vars/${stage}.tfvars
      - echo "Terraform apply"
      - terraform apply -lock=false -var-file=vars/${stage}.tfvars -auto-approve
