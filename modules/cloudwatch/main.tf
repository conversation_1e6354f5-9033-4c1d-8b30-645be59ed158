provider "aws" {
  region = var.region
}

# Data source to get all CloudWatch log groups
data "aws_cloudwatch_log_groups" "all_log_groups" {}

resource "aws_cloudwatch_log_group" "apphero_logger_log_group" {
  name              = "apphero-logs-${var.environment}"
  retention_in_days = var.environment == "prod" ? 30 : 7
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

