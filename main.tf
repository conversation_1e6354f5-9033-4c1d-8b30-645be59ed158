provider "aws" {
    region = var.region
}

terraform {
  required_version = ">= 1.5.1"
  backend "s3" {}
}

module "iam" {
    source = "./modules/iam"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
    accountId = var.accountId
    s3_role_arn = var.s3_role_arn
}

module "cloudwatch" {
    source = "./modules/cloudwatch"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
}

module "codebuild" {
    source = "./modules/codebuild"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
    accountId = var.accountId
    gus_apphero_frontend_project_name                                          = var.gus_apphero_frontend_project_name
    gus_apphero_frontend_project_build_timeout                                 = var.gus_apphero_frontend_project_build_timeout
    gus_apphero_frontend_project_source_type                                   = var.gus_apphero_frontend_project_source_type
    gus_apphero_frontend_project_environment_compute_type                      = var.gus_apphero_frontend_project_environment_compute_type
    gus_apphero_frontend_project_environment_image                             = var.gus_apphero_frontend_project_environment_image
    gus_apphero_frontend_project_environment_type                              = var.gus_apphero_frontend_project_environment_type
    gus_apphero_frontend_project_environment_image_pull_credentials_type       = var.gus_apphero_frontend_project_environment_image_pull_credentials_type
    gus_apphero_frontend_project_artifact_type                                 = var.gus_apphero_frontend_project_artifact_type
    gus_student_detail_oaf_project_name                                        = var.gus_student_detail_oaf_project_name
    gus_student_detail_oaf_project_build_timeout                               = var.gus_student_detail_oaf_project_build_timeout
    gus_student_detail_oaf_project_source_type                                 = var.gus_student_detail_oaf_project_source_type
    gus_student_detail_oaf_project_environment_compute_type                    = var.gus_student_detail_oaf_project_environment_compute_type
    gus_student_detail_oaf_project_environment_image                           = var.gus_student_detail_oaf_project_environment_image
    gus_student_detail_oaf_project_environment_type                            = var.gus_student_detail_oaf_project_environment_type
    gus_student_detail_oaf_project_environment_image_pull_credentials_type     = var.gus_student_detail_oaf_project_environment_image_pull_credentials_type
    gus_student_detail_oaf_project_artifact_type                               = var.gus_student_detail_oaf_project_artifact_type
    gus_student_detail_backend_project_environment_image                       = var.gus_student_detail_backend_project_environment_image
    gus_student_detail_backend_project_build_timeout                           = var.gus_student_detail_backend_project_build_timeout
    gus_student_detail_backend_project_source_type                             = var.gus_student_detail_backend_project_source_type
    gus_student_detail_backend_project_name                                    = var.gus_student_detail_backend_project_name
    gus_student_detail_backend_project_environment_type                        = var.gus_student_detail_backend_project_environment_type
    gus_student_detail_backend_project_artifact_type                           = var.gus_student_detail_backend_project_artifact_type
    gus_student_detail_backend_project_environment_compute_type                = var.gus_student_detail_backend_project_environment_compute_type
    gus_student_detail_backend_project_environment_image_pull_credentials_type = var.gus_student_detail_backend_project_environment_image_pull_credentials_type
    apphero_sf_sync_service_project_name                                       = var.apphero_sf_sync_service_project_name
    apphero_sf_sync_service_project_build_timeout                              = var.apphero_sf_sync_service_project_build_timeout
    apphero_sf_sync_service_project_source_type                                = var.apphero_sf_sync_service_project_source_type
    apphero_sf_sync_service_project_environment_compute_type                   = var.apphero_sf_sync_service_project_environment_compute_type
    apphero_sf_sync_service_project_environment_image                          = var.apphero_sf_sync_service_project_environment_image
    apphero_sf_sync_service_project_environment_type                           = var.apphero_sf_sync_service_project_environment_type
    apphero_sf_sync_service_project_environment_image_pull_credentials_type    = var.apphero_sf_sync_service_project_environment_image_pull_credentials_type
    apphero_sf_sync_service_project_artifact_type                              = var.apphero_sf_sync_service_project_artifact_type
}

module "codepipeline" {
  environment_tag                                                   = var.environment_tag
  source                                                            = "./modules/codepipeline"
  region                                                            = var.region
  environment                                                       = var.environment
  accountId                                                         = var.accountId
  gus_student_detail_oaf_pipeline_name                              = var.gus_student_detail_oaf_pipeline_name
  gus_student_detail_oaf_pipeline_artifact_store_location           = var.gus_student_detail_oaf_pipeline_artifact_store_location
  gus_student_detail_oaf_pipeline_artifact_store_type               = var.gus_student_detail_oaf_pipeline_artifact_store_type
  gus_student_detail_oaf_pipeline_source_config_repository_name     = var.gus_student_detail_oaf_pipeline_source_config_repository_name
  gus_student_detail_oaf_pipeline_source_config_branch_name         = var.gus_student_detail_oaf_pipeline_source_config_branch_name
  gus_student_detail_oaf_pipeline_project_name                      = var.gus_student_detail_oaf_pipeline_project_name
  gus_student_detail_backend_pipeline_artifact_store_location       = var.gus_student_detail_backend_pipeline_artifact_store_location
  gus_student_detail_backend_pipeline_artifact_store_type           = var.gus_student_detail_backend_pipeline_artifact_store_type
  gus_student_detail_backend_pipeline_name                          = var.gus_student_detail_backend_pipeline_name
  gus_student_detail_backend_pipeline_project_name                  = var.gus_student_detail_backend_pipeline_project_name
  gus_student_detail_backend_pipeline_source_config_branch_name     = var.gus_student_detail_backend_pipeline_source_config_branch_name
  gus_student_detail_backend_pipeline_source_config_repository_name = var.gus_student_detail_backend_pipeline_source_config_repository_name
  gus_apphero_frontend_pipeline_name                                = var.gus_apphero_frontend_pipeline_name
  gus_apphero_frontend_pipeline_artifact_store_location             = var.gus_apphero_frontend_pipeline_artifact_store_location
  gus_apphero_frontend_pipeline_artifact_store_type                 = var.gus_apphero_frontend_pipeline_artifact_store_type
  gus_apphero_frontend_pipeline_source_config_repository_name       = var.gus_apphero_frontend_pipeline_source_config_repository_name
  gus_apphero_frontend_pipeline_source_config_branch_name           = var.gus_apphero_frontend_pipeline_source_config_branch_name
  gus_apphero_frontend_pipeline_project_name                        = var.gus_apphero_frontend_pipeline_project_name
  apphero_backend_pipeline_name                                     = var.apphero_backend_pipeline_name
  apphero_backend_pipeline_artifact_store_location                  = var.apphero_backend_pipeline_artifact_store_location
  apphero_backend_pipeline_artifact_store_type                      = var.apphero_backend_pipeline_artifact_store_type
  apphero_backend_pipeline_source_config_repository_name            = var.apphero_backend_pipeline_source_config_repository_name
  apphero_backend_pipeline_source_config_branch_name                = var.apphero_backend_pipeline_source_config_branch_name
  apphero_backend_pipeline_project_name                             = var.apphero_backend_pipeline_project_name
  apphero_sf_sync_service_pipeline_name                                = var.apphero_sf_sync_service_pipeline_name
  apphero_sf_sync_service_pipeline_artifact_store_location             = var.apphero_sf_sync_service_pipeline_artifact_store_location
  apphero_sf_sync_service_pipeline_artifact_store_type                 = var.apphero_sf_sync_service_pipeline_artifact_store_type
  apphero_sf_sync_service_pipeline_source_config_repository_name       = var.apphero_sf_sync_service_pipeline_source_config_repository_name
  apphero_sf_sync_service_pipeline_source_config_branch_name           = var.apphero_sf_sync_service_pipeline_source_config_branch_name
  apphero_sf_sync_service_pipeline_project_name                        = var.apphero_sf_sync_service_pipeline_project_name
}

module "s3" {
    source = "./modules/s3"
    environment = var.environment
    environment_tag = var.environment_tag
}

module "sns" {
    source = "./modules/sns"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
}

module "ses" {
    source = "./modules/ses"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
    accountId = var.accountId
    apphero_email_identity = var.apphero_email_identity
}

module "athena" {
  source                                      = "./modules/athena"
  region                                      = var.region
  environment                                 = var.environment
  environment_tag                             = var.environment_tag
  apphero_workgroup_name                      = var.apphero_workgroup_name
}

module "glue" {
    source                                        = "./modules/glue"
    region                                        = var.region
    environment                                   = var.environment
    environment_tag                               = var.environment_tag
    aws_glue_catalog_table_type                   = var.aws_glue_catalog_table_type
    storage_descriptor_location                   = var.storage_descriptor_location
    storage_descriptor_input_format               = var.storage_descriptor_input_format
    storage_descriptor_output_format              = var.storage_descriptor_output_format
    ser_de_info_name                              = var.ser_de_info_name
    ser_de_info_serialization_library             = var.ser_de_info_serialization_library
}

module "cognito" {
    source                                                       = "./modules/cognito"
    region                                                       = var.region
    environment                                                  = var.environment
    environment_tag                                              = var.environment_tag
    accountId                                                    = var.accountId
    cognito_linkedin_identity_provider_client_id                 = var.cognito_linkedin_identity_provider_client_id
    cognito_linkedin_identity_provider_client_secret             = var.cognito_linkedin_identity_provider_client_secret
    cognito_linkedin_identity_provider_oidc_issuer               = var.cognito_linkedin_identity_provider_oidc_issuer
    cognito_linkedin_identity_provider_authorize_scopes          = var.cognito_linkedin_identity_provider_authorize_scopes
    cognito_linkedin_identity_provider_attributes_request_method = var.cognito_linkedin_identity_provider_attributes_request_method
    cognito_user_pool_client_allowed_oauth_flows                 = var.cognito_user_pool_client_allowed_oauth_flows
    cognito_user_pool_client_allowed_oauth_scopes                = var.cognito_user_pool_client_allowed_oauth_scopes
    cognito_user_pool_client_explicit_auth_flows                 = var.cognito_user_pool_client_explicit_auth_flows
    cognito_user_pool_client_callback_urls                       = var.cognito_user_pool_client_callback_urls
    cognito_user_pool_client_logout_urls                         = var.cognito_user_pool_client_logout_urls
    cognito_user_pool_client_supported_identity_providers        = var.cognito_user_pool_client_supported_identity_providers
    ses_mailer_arn                                               = var.ses_mailer_arn
    apphero_gateway_custom_domain                                = var.apphero_gateway_custom_domain
    api_gateway_certificate_acm_certificate_arn                  = var.api_gateway_certificate_acm_certificate_arn
    cognito_apphero_custom_domain                                = var.cognito_apphero_custom_domain
}

module "apigateway" {
    source = "./modules/apigateway"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
    accountId  = var.accountId
    cognito_user_pool_id        = var.cognito_user_pool_id
    apphero_gateway_custom_domain  = var.apphero_gateway_custom_domain
    api_gateway_certificate_acm_certificate_arn = var.api_gateway_certificate_acm_certificate_arn
}

module "student_detail_apigateway" {
    source = "./modules/gusstudentdetailapigateway"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
    accountId  = var.accountId
    cognito_user_pool_id        = var.cognito_user_pool_id
    gus_studentdetail_gateway_custom_domain  = var.gus_studentdetail_gateway_custom_domain
    api_gateway_certificate_acm_certificate_arn = var.api_gateway_certificate_acm_certificate_arn
}

module "cloudfront" {
  source                                                            = "./modules/cloudfront"
  region                                                            = var.region
  environment                                                       = var.environment
  environment_tag                                                   = var.environment_tag
  s3_distribution_origin_protocol_policy                            = var.s3_distribution_origin_protocol_policy
  s3_distribution_origin_ssl_protocols                              = var.s3_distribution_origin_ssl_protocols
  s3_distribution_http_port                                         = var.s3_distribution_http_port
  s3_distribution_https_port                                        = var.s3_distribution_https_port
  s3_distribution_enabled                                           = var.s3_distribution_enabled
  s3_distribution_is_ipv6_enabled                                   = var.s3_distribution_is_ipv6_enabled
  apphero_alternative_domain                                        = var.apphero_alternative_domain
  s3_distribution_default_cache_behavior_allowed_methods            = var.s3_distribution_default_cache_behavior_allowed_methods
  s3_distribution_default_cache_behavior_cached_methods             = var.s3_distribution_default_cache_behavior_cached_methods
  s3_distribution_default_cache_behavior_cache_policy_id            = var.s3_distribution_default_cache_behavior_cache_policy_id
  s3_distribution_default_cache_behavior_response_headers_policy_id = var.s3_distribution_default_cache_behavior_response_headers_policy_id
  s3_distribution_viewer_protocol_policy                            = var.s3_distribution_viewer_protocol_policy
  s3_distribution_geo_restriction_restriction_type                  = var.s3_distribution_geo_restriction_restriction_type
  s3_distribution_viewer_certificate_cloudfront_default_certificate = var.s3_distribution_viewer_certificate_cloudfront_default_certificate
  s3_distribution_viewer_certificate_acm_certificate_arn            = var.s3_distribution_viewer_certificate_acm_certificate_arn
  s3_distribution_student_detail_security_policy                    = var.s3_distribution_student_detail_security_policy
  student_detail_alternative_domain                                 = var.student_detail_alternative_domain
}

module "lambda" {
    source = "./modules/lambda"
    region = var.region
    environment = var.environment
    environment_tag = var.environment_tag
    accountId  = var.accountId
    gus_middleware_api = var.gus_middleware_api
    gus_apphero_api  = var.gus_apphero_api
    cognito_user_pool_id = var.cognito_user_pool_id
    api_id = var.api_id
    apphero_consumer_api_key = var.apphero_consumer_api_key
    ses_mailer = var.ses_mailer
    mailer_logo = var.mailer_logo
    gus_apphero_graphQl_key = var.gus_apphero_graphQl_key
    gus_apphero_graphQl_api = var.gus_apphero_graphQl_api
    forget_password_mailer_img = var.forget_password_mailer_img
    apphero_login_url = var.apphero_login_url
    apphero_notification_table = var.apphero_notification_table
}

module "apphero-notify-listener" {
  source                      = "./modules/ecs/apphero-notify-listener"
  region                      = var.region
  environment                 = var.environment
  accountId                   = var.accountId
}

module "cluster" {
  source                      = "./modules/ecs/cluster"
  region                      = var.region
  environment                 = var.environment
  accountId                   = var.accountId
}

module "sqs" {
  source = "./modules/sqs"
  environment = var.environment
  environment_tag = var.environment_tag
}

module "apphero-chatbot-pipeline" {
  source = "./modules/apphero-chatbot-pipeline"
  environment = var.environment
  environment_tag = var.environment_tag
  accountId = var.accountId
  region = var.region
  brand = var.brand
  projectname = var.projectname
  repositoryname = var.repositoryname
  teamname = var.teamname
}

