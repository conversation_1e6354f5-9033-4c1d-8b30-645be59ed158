resource "aws_sesv2_email_identity" "apphero_email_identity" {
  email_identity         = var.apphero_email_identity
  configuration_set_name = aws_sesv2_configuration_set.apphero_mailer.configuration_set_name
}

resource "aws_sesv2_configuration_set" "apphero_mailer" {
  configuration_set_name = "apphero-mailer-${var.environment}"

  tags = {
    Environment = var.environment_tag
    Project     = "APPHERO"
    Team        = "EIP Development Team"
  }
}

resource "aws_sesv2_configuration_set_event_destination" "apphero_mailer_sns_destination" {
  configuration_set_name = aws_sesv2_configuration_set.apphero_mailer.configuration_set_name
  event_destination_name = "apphero-mailer-sns-event-destination"

  event_destination {
    sns_destination {
      topic_arn = "arn:aws:sns:${var.region}:${var.accountId}:apphero-mailer-events-tracker-${var.environment}"
    }

    enabled              = true
    matching_event_types = [
      "SEND",
      "REJECT",
      "BOUNCE",
      "COMPLAINT",
      "DELIVERY",
      "O<PERSON><PERSON>",
      "<PERSON><PERSON><PERSON><PERSON>",
      "DELIVERY_DELAY",
      "SUBS<PERSON>IPTION",
      "RENDERING_FAILURE"
  ]
  }
}