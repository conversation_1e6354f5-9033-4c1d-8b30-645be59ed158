resource "aws_api_gateway_rest_api" "api" {
  name        = "gus-student-detail-service-${var.environment}"
  description = "API"
  tags = {
    Environment = var.environment_tag
    Project     = "GUS_STUDENT_DETAIL"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_method" "method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_rest_api.api.root_resource_id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_rest_api.api.root_resource_id
  http_method          = aws_api_gateway_method.method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = aws_api_gateway_method_response.method_response.status_code
  depends_on = [
    aws_api_gateway_integration.integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }

  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#STUDENT DETAIL LOOKUP

resource "aws_api_gateway_resource" "student_detail_lookup_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "lookup"
}


resource "aws_api_gateway_resource" "student_detail_proxy_lookup_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_detail_lookup_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "student_detail_proxy_lookup_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_detail_proxy_lookup_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method             = aws_api_gateway_method.student_detail_proxy_lookup_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_detail_proxy_lookup_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_detail_proxy_lookup_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method          = aws_api_gateway_method.student_detail_proxy_lookup_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_detail_proxy_lookup_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_lookup_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_detail_proxy_lookup_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_lookup_options_method.http_method
  status_code = aws_api_gateway_method_response.student_detail_proxy_lookup_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_detail_proxy_lookup_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#STUDENT FILES
resource "aws_api_gateway_resource" "student_file_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "studentfile"
}

resource "aws_api_gateway_method" "student_file_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_file_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_file_resource.id
  http_method             = aws_api_gateway_method.student_file_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_file_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_file_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.student_file_resource.id
  http_method          = aws_api_gateway_method.student_file_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_file_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_resource.id
  http_method = aws_api_gateway_method.student_file_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_file_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_resource.id
  http_method = aws_api_gateway_method.student_file_options_method.http_method
  status_code = aws_api_gateway_method_response.student_file_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_file_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_resource" "student_file_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_file_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "student_file_proxy_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_file_proxy_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method             = aws_api_gateway_method.student_file_proxy_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_file_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_file_proxy_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method          = aws_api_gateway_method.student_file_proxy_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_file_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method = aws_api_gateway_method.student_file_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_file_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method = aws_api_gateway_method.student_file_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.student_file_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_file_proxy_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#STUDENT DETAIL
resource "aws_api_gateway_resource" "student_detail_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "studentdetail"
}

resource "aws_api_gateway_method" "student_detail_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_detail_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_resource.id
  http_method             = aws_api_gateway_method.student_detail_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_detail_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_detail_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.student_detail_resource.id
  http_method          = aws_api_gateway_method.student_detail_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_detail_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_resource.id
  http_method = aws_api_gateway_method.student_detail_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_detail_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_resource.id
  http_method = aws_api_gateway_method.student_detail_options_method.http_method
  status_code = aws_api_gateway_method_response.student_detail_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_detail_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_resource" "student_detail_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_detail_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "student_detail_proxy_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_detail_proxy_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method             = aws_api_gateway_method.student_detail_proxy_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_detail_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_detail_proxy_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method          = aws_api_gateway_method.student_detail_proxy_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_detail_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_detail_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.student_detail_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_detail_proxy_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#gateway response
resource "aws_api_gateway_gateway_response" "access_denied_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "ACCESS_DENIED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "unauthorized_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "UNAUTHORIZED"
  status_code   = 401
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "waf_filtered_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "WAF_FILTERED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "request_too_large_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "REQUEST_TOO_LARGE"
  status_code   = 413
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_apikey_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "INVALID_API_KEY"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_4xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "DEFAULT_4XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "quota_exceeded_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "QUOTA_EXCEEDED"
  status_code   = 429
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "throttled_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "THROTTLED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}



resource "aws_api_gateway_gateway_response" "bad_request_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "BAD_REQUEST_BODY"
  status_code   = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_signature_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "INVALID_SIGNATURE"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "missing_athentication_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "MISSING_AUTHENTICATION_TOKEN"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "bad_request_parameter_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "BAD_REQUEST_PARAMETERS"
  status_code   = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_5xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "DEFAULT_5XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "expired_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "EXPIRED_TOKEN"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "resource_not_found_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "RESOURCE_NOT_FOUND"
  status_code   = 404
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}
resource "null_resource" "delete_stage" {
  provisioner "local-exec" {
    command = "echo 'Hello, Terraform!'"
  }
}
resource "aws_api_gateway_deployment" "gus_studentdetail_deployment" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  variables = {
    deployed_at = timestamp()
  }
  triggers = {
    redeployment = sha1(jsonencode(aws_api_gateway_rest_api.api.body))
  }
  lifecycle {
    create_before_destroy = true
  }
  depends_on = [
    aws_api_gateway_method.method,
    aws_api_gateway_method.student_detail_proxy_lookup_method,
    aws_api_gateway_method.student_detail_proxy_lookup_options_method,
    aws_api_gateway_method.student_file_method,
    aws_api_gateway_method.student_file_options_method,
    aws_api_gateway_method.student_file_proxy_method,
    aws_api_gateway_method.student_file_proxy_options_method,
    aws_api_gateway_method.student_detail_method,
    aws_api_gateway_method.student_detail_options_method,
    aws_api_gateway_method.student_detail_proxy_method,
    aws_api_gateway_method.student_detail_proxy_options_method,
    aws_api_gateway_integration.integration,
    aws_api_gateway_integration.student_detail_proxy_lookup_integration,
    aws_api_gateway_integration.student_detail_proxy_lookup_options_integration,
    aws_api_gateway_integration.student_file_integration,
    aws_api_gateway_integration.student_file_options_integration,
    aws_api_gateway_integration.student_file_proxy_integration,
    aws_api_gateway_integration.student_file_proxy_options_integration,
    aws_api_gateway_integration.student_detail_integration,
    aws_api_gateway_integration.student_detail_options_integration,
    aws_api_gateway_integration.student_detail_proxy_integration,
    aws_api_gateway_integration.student_detail_proxy_options_integration
  ]
}
resource "aws_api_gateway_stage" "gus_studentdetail_deployment_stage" {
  deployment_id        = aws_api_gateway_deployment.gus_studentdetail_deployment.id
  stage_name           = var.environment
  rest_api_id          = aws_api_gateway_rest_api.api.id
  xray_tracing_enabled = true
  tags = {
    Environment = var.environment_tag
    Project     = "STUDENTDETAIL"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_domain_name" "gus_studentdetail_domain" {
  domain_name     = var.gus_studentdetail_gateway_custom_domain
  certificate_arn = var.api_gateway_certificate_acm_certificate_arn
  endpoint_configuration {
    types = ["EDGE"]
  }
}

resource "aws_api_gateway_base_path_mapping" "gus_studentdetail_gateway_mapping" {
  api_id      = aws_api_gateway_rest_api.api.id
  domain_name = aws_api_gateway_domain_name.gus_studentdetail_domain.id
  stage_name  = aws_api_gateway_stage.gus_studentdetail_deployment_stage.stage_name
}

output "invoke_url" {
  value = aws_api_gateway_stage.gus_studentdetail_deployment_stage.invoke_url
}
