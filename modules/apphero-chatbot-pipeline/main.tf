module "codepipeline-module" {
  source                    = "../codepipeline-module"
  brand                     = var.brand
  environment               = var.environment
  accountId                 = var.accountId
  repositoryname            = var.repositoryname
  region                    = var.region
  teamname                  = var.teamname
  projectname               = var.projectname
  aws_cloudwatch_event_rule = var.brand
  environment_tag           = var.environment_tag
  service                   = "/aws/ecs"
}

module "codebuild-module" {
  source          = "../codebuild-module"
  brand           = var.brand
  environment     = var.environment
  environment_tag = var.environment_tag
  iam_role_arn    = aws_iam_role.codebuild_role.arn
}