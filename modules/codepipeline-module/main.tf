resource "aws_s3_bucket" "artifact_bucket" {
  bucket = "${var.environment}-${var.brand}-codepipeline-artifacts"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "gus-devops-infra"
    TEAM        = "Devops Team"
  }
}

resource "aws_iam_role" "codepipeline_role" {
  name = "${var.environment}-${var.brand}-codepipeline-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action    = "sts:AssumeRole"
        Effect    = "Allow"
        Principal = { Service = "codepipeline.amazonaws.com" }
      }
    ]
  })
}

resource "aws_iam_policy" "codecommit_access" {
  name = "${var.brand}-${var.environment}-codecommit-access"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "codecommit:GetBranch",
          "codecommit:GetCommit",
          "codecommit:UploadArchive",
          "codecommit:GetUploadArchiveStatus",
          "codecommit:CancelUploadArchive",
          "s3:GetObject",
          "s3:PutObject",
          "codebuild:StartBuild",
          "codebuild:BatchGetBuilds",
          "codebuild:BatchGetBuildBatches",
          "codebuild:StartBuildBatch",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "attach_codecommit" {
  role       = aws_iam_role.codepipeline_role.name
  policy_arn = aws_iam_policy.codecommit_access.arn
}


resource "aws_codepipeline" "pipeline" {
  name     = "${var.brand}-pipeline-${var.environment}"
  role_arn = aws_iam_role.codepipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.artifact_bucket.bucket
    type     = "S3"
  }

  stage {
    name = "Source"
    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        RepositoryName       = var.repositoryname
        BranchName           = var.environment
        PollForSourceChanges = "true"
      }
    }
  }

  stage {
    name = "Build"
    action {
      name             = "Build"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["source_output"]
      output_artifacts = ["build_output"]

      configuration = {
        ProjectName = "${var.brand}-${var.environment}-codebuild-project"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = var.projectname
    TEAM        = var.teamname
  }
  depends_on = [aws_s3_bucket.artifact_bucket]
}

resource "aws_cloudwatch_event_rule" "codebuild_trigger" {
  name = "${var.aws_cloudwatch_event_rule}-${var.environment}"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:${var.repositoryname}"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = var.projectname
    TEAM        = var.teamname
  }
}

resource "aws_cloudwatch_event_target" "codebuild_target" {
  rule     = aws_cloudwatch_event_rule.codebuild_trigger.name
  arn      = aws_codepipeline.pipeline.arn
  role_arn = "arn:aws:iam::${var.accountId}:role/gus-ibd-devops-infra-codebuild-service-role-${var.environment}"
}

//devops-infra
resource "aws_cloudwatch_log_group" "cloudwatch_log" {
  name = "/aws/codepipeline/${var.environment}/${var.brand}"
  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7 # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "gus-devops-infra"
    TEAM        = "Devops Team"
  }
}