region="eu-west-1"
environment="prod"
environment_tag="PROD"

//IAM
accountId       = "************"
s3_role_arn             = "arn:aws:iam::************:role/s3CrossAccountAccessRole-prod"

//codebuild
gus_apphero_frontend_project_name                                          = "gus-apphero-frontend"
gus_apphero_frontend_project_build_timeout                                 = "5"
gus_apphero_frontend_project_source_type                                   = "CODEPIPELINE"
gus_apphero_frontend_project_environment_compute_type                      = "BUILD_GENERAL1_MEDIUM"
gus_apphero_frontend_project_environment_image                             = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
gus_apphero_frontend_project_environment_type                              = "LINUX_CONTAINER"
gus_apphero_frontend_project_environment_image_pull_credentials_type       = "CODEBUILD"
gus_apphero_frontend_project_artifact_type                                 = "CODEPIPELINE"
gus_student_detail_oaf_project_name                                        = "gus-student-detail-oaf-frontend"
gus_student_detail_oaf_project_build_timeout                               = "5"
gus_student_detail_oaf_project_source_type                                 = "CODEPIPELINE"
gus_student_detail_oaf_project_environment_compute_type                    = "BUILD_GENERAL1_SMALL"
gus_student_detail_oaf_project_environment_image                           = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
gus_student_detail_oaf_project_environment_type                            = "LINUX_CONTAINER"
gus_student_detail_oaf_project_environment_image_pull_credentials_type     = "CODEBUILD"
gus_student_detail_oaf_project_artifact_type                               = "CODEPIPELINE"
gus_student_detail_backend_project_name                                    = "gus-student-detail-backend-service"
gus_student_detail_backend_project_build_timeout                           = "5"
gus_student_detail_backend_project_source_type                             = "CODEPIPELINE"
gus_student_detail_backend_project_environment_compute_type                = "BUILD_GENERAL1_SMALL"
gus_student_detail_backend_project_environment_image                       = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
gus_student_detail_backend_project_environment_type                        = "LINUX_CONTAINER"
gus_student_detail_backend_project_environment_image_pull_credentials_type = "CODEBUILD"
gus_student_detail_backend_project_artifact_type                           = "CODEPIPELINE"
apphero_sf_sync_service_project_name                                           = "apphero-sf-sync-service"
apphero_sf_sync_service_project_build_timeout                                     = "5"
apphero_sf_sync_service_project_source_type                                       = "CODEPIPELINE"
apphero_sf_sync_service_project_environment_compute_type                          = "BUILD_GENERAL1_SMALL"
apphero_sf_sync_service_project_environment_image                                 = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
apphero_sf_sync_service_project_environment_type                                  = "LINUX_CONTAINER"
apphero_sf_sync_service_project_environment_image_pull_credentials_type           = "CODEBUILD"
apphero_sf_sync_service_project_artifact_type                                     = "CODEPIPELINE"

//codepipeline
gus_student_detail_oaf_pipeline_name                              = "gus-student-detail-oaf-frontend"
gus_student_detail_oaf_pipeline_artifact_store_location           = "gus-student-detail-oaf-frontend"
gus_student_detail_oaf_pipeline_artifact_store_type               = "S3"
gus_student_detail_oaf_pipeline_source_config_repository_name     = "gus-student-detail-oaf-frontend"
gus_student_detail_oaf_pipeline_source_config_branch_name         = "prod"
gus_student_detail_oaf_pipeline_project_name                      = "gus-student-detail-oaf-frontend"
gus_student_detail_backend_pipeline_name                          = "gus-student-detail-backend-service"
gus_student_detail_backend_pipeline_artifact_store_location       = "gus-student-detail-backend-service"
gus_student_detail_backend_pipeline_artifact_store_type           = "S3"
gus_student_detail_backend_pipeline_source_config_repository_name = "gus-student-detail-backend-service"
gus_student_detail_backend_pipeline_source_config_branch_name     = "prod"
gus_student_detail_backend_pipeline_project_name                  = "gus-student-detail-backend-service"
gus_apphero_frontend_pipeline_name                                = "gus-apphero-frontend"
gus_apphero_frontend_pipeline_artifact_store_location             = "gus-apphero-frontend"
gus_apphero_frontend_pipeline_artifact_store_type                 = "S3"
gus_apphero_frontend_pipeline_source_config_repository_name       = "gus-apphero-frontend"
gus_apphero_frontend_pipeline_source_config_branch_name           = "prod"
gus_apphero_frontend_pipeline_project_name                        = "gus-apphero-frontend"
apphero_backend_pipeline_name                                     = "apphero-backend-service"
apphero_backend_pipeline_artifact_store_location                  = "apphero-backend-service"
apphero_backend_pipeline_artifact_store_type                      = "S3"
apphero_backend_pipeline_source_config_repository_name            = "apphero-backend-service"
apphero_backend_pipeline_source_config_branch_name                = "prod"
apphero_backend_pipeline_project_name                             = "apphero-backend-service"
apphero_sf_sync_service_pipeline_name                                = "apphero-sf-sync-service"
apphero_sf_sync_service_pipeline_artifact_store_location             = "apphero-sf-sync-service"
apphero_sf_sync_service_pipeline_artifact_store_type                 = "S3"
apphero_sf_sync_service_pipeline_source_config_repository_name       = "apphero-sf-sync-service"
apphero_sf_sync_service_pipeline_source_config_branch_name           = "prod"
apphero_sf_sync_service_pipeline_project_name                        = "apphero-sf-sync-service"

#cognito userpool
ses_mailer_arn = "arn:aws:ses:eu-west-1:************:identity/<EMAIL>"
cognito_apphero_custom_domain = "apphero-authprovider.apphero.io"
#cognito_identity_providers
cognito_linkedin_identity_provider_client_id                 = "8642sqv4vubjow"
cognito_linkedin_identity_provider_client_secret             = "WElCywSoNjWuR3J0"
cognito_linkedin_identity_provider_oidc_issuer               = "https://www.linkedin.com/oauth"
cognito_linkedin_identity_provider_authorize_scopes          = "email openid profile"
cognito_linkedin_identity_provider_attributes_request_method = "GET"
#cognito_user_pool_client
cognito_user_pool_client_allowed_oauth_flows          = ["code"]
cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
cognito_user_pool_client_callback_urls                = ["http://localhost:3000/linkedin-loader", "https://d1kqguj35zfiho.cloudfront.net/linkedin-loader","https://www.apphero.io/linkedin-loader"]
cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login", "https://d1kqguj35zfiho.cloudfront.net/login","https://www.apphero.io/login"]
cognito_user_pool_client_supported_identity_providers = ["COGNITO", "linkedin"]

#apigateway
cognito_user_pool_id        = "eu-west-1_Q5t0ZyN4K" //update after creation
apphero_gateway_custom_domain = "api.apphero.io"
api_gateway_certificate_acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/c767ec40-a121-4998-9185-8493e7a45ad9"
gus_studentdetail_gateway_custom_domain = "profile-api.apphero.io"
apphero_consumer_api_key = "xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ" //Check on this ....


#cloudfront
s3_distribution_http_port                                         = 80
s3_distribution_https_port                                        = 443
s3_distribution_origin_protocol_policy                            = "http-only"
s3_distribution_origin_ssl_protocols                              = ["TLSv1.2"]
s3_distribution_enabled                                           = true
apphero_alternative_domain                                        = ["www.apphero.io","apphero.io"]
s3_distribution_is_ipv6_enabled                                   = true
s3_distribution_default_cache_behavior_allowed_methods            = ["GET", "HEAD", "OPTIONS"]
s3_distribution_default_cache_behavior_cached_methods             = ["GET", "HEAD"]
s3_distribution_default_cache_behavior_cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
s3_distribution_default_cache_behavior_response_headers_policy_id = "eaab4381-ed33-4a86-88ca-d9558dc6cd63"
s3_distribution_viewer_protocol_policy                            = "redirect-to-https"
s3_distribution_geo_restriction_restriction_type                  = "none"
s3_distribution_viewer_certificate_cloudfront_default_certificate = true
s3_distribution_viewer_certificate_acm_certificate_arn            = "arn:aws:acm:us-east-1:************:certificate/c767ec40-a121-4998-9185-8493e7a45ad9"
s3_distribution_student_detail_security_policy                    = "frame-ancestors stage.apphero.io"
student_detail_alternative_domain                                 = ["profile.apphero.io"]

#lambda
gus_middleware_api = "https://api.guseip.io"
gus_apphero_api    = "https://api.apphero.io"
api_id             = "ycwq8lve4d"
ses_mailer         = "<EMAIL>"
mailer_logo                                          = "https://iexjvc.stripocdn.email/content/guids/CABINET_b0e19c10e9cf66955a60833e2e3895dd86ca1eb9a9033c17773af551761d4060/images/stylemonochrome_size16_inversionon_version1.png"
gus_apphero_graphQl_api                              = "https://c7pzxu2tordhxmvzwguzz37iku.appsync-api.eu-west-1.amazonaws.com/graphql"
gus_apphero_graphQl_key                              = "da2-e2t5kd72wbdjjn6ayec7c6zawa"
forget_password_mailer_img                           = "https://iexjvc.stripocdn.email/content/guids/CABINET_bbb59600eb73033f7bf820b32916a7f85a3b885e52792a2a3376b34d6e5e5134/images/paassword_copy.png"
apphero_login_url                                    =  "https://www.apphero.io/login"
apphero_notification_table = "apphero-notifications-prod"


 #glue
 aws_glue_catalog_table_type       = "EXTERNAL_TABLE"
 storage_descriptor_location       = "s3://apphero-logs-exports-dev/logs/"
 storage_descriptor_input_format   = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat"
 storage_descriptor_output_format  = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat"
 ser_de_info_name                  = "gus-stream"
 ser_de_info_serialization_library = "org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe"

 #athena
 apphero_workgroup_name = "apphero"

 #ses
 apphero_email_identity = "<EMAIL>"

 # Apphero Chatbot codebuild & Codepipeline
brand                     = "apphero-chatbot"
repositoryname            = "apphero-chatbot-case-handler"
teamname                  = "devops"
projectname               = "apphero-chatbot"
aws_cloudwatch_event_rule = "apphero-chatbot" 