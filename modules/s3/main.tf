//gus-student-detail-backend-service
resource "aws_s3_bucket" "gus_student_detail_backend" {
  bucket = "gus-student-detail-backend-service-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

//profile-picture
resource "aws_s3_bucket" "apphero_profile_picture" {
  bucket = "apphero-profile-picture-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_public_access_block" "apphero_profile_picture_public_access_block" {
  bucket = aws_s3_bucket.apphero_profile_picture.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "apphero_profile_picture_access_policy" {
  bucket = aws_s3_bucket.apphero_profile_picture.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Principal : "*",
        Resource : [
          "${aws_s3_bucket.apphero_profile_picture.arn}",
          "${aws_s3_bucket.apphero_profile_picture.arn}/*",
        ]
        Action : [
          "s3:GetObject",
          "s3:ListBucket"
        ]
      },
    ]
  })
}

//apphero-sf-sync-service
resource "aws_s3_bucket" "apphero-sf-sync-service" {
  bucket = "apphero-sf-sync-service-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

//apphero-backend
resource "aws_s3_bucket" "apphero-backend-service" {
  bucket = "apphero-backend-service-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

//gus_student_detail_oaf
resource "aws_s3_bucket" "gus_student_detail_oaf_frontend" {
  bucket = "gus-student-detail-oaf-frontend-${var.environment}" //${var.environment}
  website {
    index_document = "index.html"
    error_document = "index.html"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

output "gus_student_detail_oaf_frontend_website_endpoint" {
  value = aws_s3_bucket.gus_student_detail_oaf_frontend.website_endpoint
}

resource "aws_s3_bucket_ownership_controls" "gus_student_detail_oaf_frontend_ownership_controls" {
  bucket = aws_s3_bucket.gus_student_detail_oaf_frontend.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "gus_student_detail_oaf_frontend_public_access_block" {
  bucket = aws_s3_bucket.gus_student_detail_oaf_frontend.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "gus_student_detail_oaf_frontend_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.gus_student_detail_oaf_frontend_ownership_controls,
    aws_s3_bucket_public_access_block.gus_student_detail_oaf_frontend_public_access_block,
  ]

  bucket = aws_s3_bucket.gus_student_detail_oaf_frontend.id
  acl    = "public-read"
}

resource "aws_s3_bucket_policy" "gus_student_detail_oaf_frontend_access_policy" {
  bucket = aws_s3_bucket.gus_student_detail_oaf_frontend.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Principal : "*",
        Resource : [
          aws_s3_bucket.gus_student_detail_oaf_frontend.arn,
          "${aws_s3_bucket.gus_student_detail_oaf_frontend.arn}/*",
        ]
        Action : [
          "s3:GetObject",
          "s3:ListBucket",
        ]
      },
    ]
  })
}

resource "aws_s3_bucket_cors_configuration" "gus_student_detail_oaf_frontend_cors_policy" {
  bucket = aws_s3_bucket.gus_student_detail_oaf_frontend.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "DELETE"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = [""]
    max_age_seconds = 3000
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = ["Access-Control-Allow-Origin"]
  }

  cors_rule {
    allowed_headers = [""]
    allowed_methods = ["GET"]
    allowed_origins = ["*"]
    expose_headers  = [""]
  }
}

//gus_apphero
resource "aws_s3_bucket" "gus_apphero_frontend" {
  bucket = "gus-apphero-frontend-${var.environment}" //${var.environment}
  website {
    index_document = "index.html"
    error_document = "index.html"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

output "gus_apphero_frontend_website_endpoint" {
  value = aws_s3_bucket.gus_apphero_frontend.website_endpoint
}

resource "aws_s3_bucket_ownership_controls" "gus_apphero_frontend_ownership_controls" {
  bucket = aws_s3_bucket.gus_apphero_frontend.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "gus_apphero_frontend_public_access_block" {
  bucket = aws_s3_bucket.gus_apphero_frontend.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "gus_apphero_frontend_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.gus_apphero_frontend_ownership_controls,
    aws_s3_bucket_public_access_block.gus_apphero_frontend_public_access_block,
  ]

  bucket = aws_s3_bucket.gus_apphero_frontend.id
  acl    = "public-read"
}

resource "aws_s3_bucket_policy" "gus_apphero_frontend_access_policy" {
  bucket = aws_s3_bucket.gus_apphero_frontend.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Principal : "*",
        Resource : [
          aws_s3_bucket.gus_apphero_frontend.arn,
          "${aws_s3_bucket.gus_apphero_frontend.arn}/*",
        ]
        Action : [
          "s3:GetObject",
          "s3:ListBucket",
        ]
      },
    ]
  })
}

resource "aws_s3_bucket_cors_configuration" "gus_apphero_frontend_cors_policy" {
  bucket = aws_s3_bucket.gus_apphero_frontend.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "DELETE"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = [""]
    max_age_seconds = 3000
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = ["Access-Control-Allow-Origin"]
  }

  cors_rule {
    allowed_headers = [""]
    allowed_methods = ["GET"]
    allowed_origins = ["*"]
    expose_headers  = [""]
  }
}

//appHero-brandImages
resource "aws_s3_bucket" "apphero-brand-logos" {
  bucket = "apphero-brand-logos-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_public_access_block" "apphero-brand-logos_public_access_block" {
  bucket = aws_s3_bucket.apphero-brand-logos.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "apphero-brand-logos_access_policy" {
  bucket = aws_s3_bucket.apphero-brand-logos.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Principal : "*",
        Resource : [
          aws_s3_bucket.apphero-brand-logos.arn,
          "${aws_s3_bucket.apphero-brand-logos.arn}/*",
        ]
        Action : [
          "s3:GetObject",
          "s3:ListBucket",
        ]
      },
    ]
  })
}

//apphero-logs-exports
resource "aws_s3_bucket" "apphero_logs_exports" {
  bucket = "apphero-logs-exports-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_ownership_controls" "apphero_logs_exports_ownership_controls" {
  bucket = aws_s3_bucket.apphero_logs_exports.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "apphero_logs_exports_public_access_block" {
  bucket = aws_s3_bucket.apphero_logs_exports.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "apphero_logs_exports_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.apphero_logs_exports_ownership_controls,
    aws_s3_bucket_public_access_block.apphero_logs_exports_public_access_block,
  ]

  bucket = aws_s3_bucket.apphero_logs_exports.id
  acl    = "public-read"
}