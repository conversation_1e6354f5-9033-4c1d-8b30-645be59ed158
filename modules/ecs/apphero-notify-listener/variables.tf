variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Choose your region"
  type        = string
}

variable "aws_vpc" {
  type = map(string)
  description = "Map of vpc for each environment"
  default = {
    dev   = "vpc-0cb918e3de95ab54f"
    prod  = "vpc-0b5982afe4e2fe673"
  }
}

variable "private_subnet_id" {
  type = map
  description = "Map of subnets for each environment"
  default = {
    dev = ["subnet-0a13cacdf1be3c00c", "subnet-0017a5a0c7217a524"]
    prod  = ["subnet-00b038041f6daa6a1", "subnet-0679057c5c7758a30"]
  }
}

variable "public_subnet_id" {
  type = map
  description = "Map of subnets for each environment"
  default = {
    dev = ["subnet-0fc3aa4235c0b4081", "subnet-00f9f6dd6aa5e0b07"]
    prod  = ["subnet-00b038041f6daa6a1", "subnet-0679057c5c7758a30"]
  }
}

variable "security_group" {
  type = map
  description = "Map of security groups for each environment"
  default = {
    dev   = ["sg-0915895f761892085" ]
    prod  = ["sg-093e9a0cf4446bc6c"]
  }
}
