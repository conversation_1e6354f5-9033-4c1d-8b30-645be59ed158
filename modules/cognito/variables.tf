variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_client_id" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_client_secret" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_oidc_issuer" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_authorize_scopes" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_attributes_request_method" {
  description = ""
  type        = string
}

variable "cognito_user_pool_client_allowed_oauth_flows" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_allowed_oauth_scopes" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_explicit_auth_flows" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_callback_urls" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_logout_urls" {
  description = ""
  type        = list(string)
}

variable "cognito_user_pool_client_supported_identity_providers" {
  description = ""
  type        = list(string)
}

variable "ses_mailer_arn" {
  description = ""
  type        = string
}

variable "apphero_gateway_custom_domain" {
  description = ""
  type        = string
}

variable "api_gateway_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}

variable "cognito_apphero_custom_domain" {
  description = ""
  type = string
}