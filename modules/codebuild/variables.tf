variable region {
  description = "Region"
  type        = string
}

variable environment {
  description = "Environment"
  type        = string  
}

variable environment_tag {
  description = "Environment tag"
  type        = string  
}

variable accountId {
  description = "Aws account id"
  type        = string  
}

//gus-apphero
variable "gus_apphero_frontend_project_name" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_source_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_apphero_frontend_project_artifact_type" {
  description = ""
  type        = string
}


//gus_student_detail_oaf
variable "gus_student_detail_oaf_project_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_source_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_oaf_project_artifact_type" {
  description = ""
  type        = string
}


//gus_student_detail_backend
variable "gus_student_detail_backend_project_name" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_build_timeout" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_source_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_image" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "gus_student_detail_backend_project_artifact_type" {
  description = ""
  type        = string
}

//apphero sf service
variable "apphero_sf_sync_service_project_name" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_build_timeout" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_source_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_image" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}

variable "apphero_sf_sync_service_project_artifact_type" {
  description = ""
  type        = string
}