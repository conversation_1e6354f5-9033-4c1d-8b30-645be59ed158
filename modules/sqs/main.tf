resource "aws_sqs_queue" "chatbot_case_request_queue" {
  name                      = "${var.environment_tag}-APPHERO-CHATBOT-CASE-REQUEST-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 90

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.chatbot_case_request_dlq.arn
    maxReceiveCount     = 1  # adjust as needed
  })

  tags = {
    Environment = var.environment
    Application = "APPHERO"
  }
}

resource "aws_sqs_queue" "chatbot_case_request_dlq" {
  name                      = "${var.environment_tag}-APPHERO-CHATBOT-CASE-REQUEST-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 90

  tags = {
    Environment = var.environment
    Application = "APPHERO"
  }
}


