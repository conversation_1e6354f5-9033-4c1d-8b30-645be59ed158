variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "The environment (e.g., dev, prod)"
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = ""
  type        = string
}

variable "gus_middleware_api" {
  description = ""
  type        = string
}

variable "gus_apphero_api" {
  description = ""
  type        = string
}

variable "cognito_user_pool_id" {
  description = ""
  type        = string
}

variable "api_id" {
  description = ""
  type        = string
}

variable "apphero_consumer_api_key" {
  description = ""
  type        = string
}

variable "ses_mailer"{
  description = ""
  type        = string
}

variable "mailer_logo"{
  description = ""
  type        = string
}

variable "gus_apphero_graphQl_key" {
  description = ""
  type        = string
}

variable "gus_apphero_graphQl_api" {
  description = ""
  type        = string
}

variable "forget_password_mailer_img"{
  description = ""
  type        = string
}

variable "apphero_login_url"{
  description = ""
  type        = string
}

variable "apphero_notification_table"{
  description = ""
  type        = string
}
