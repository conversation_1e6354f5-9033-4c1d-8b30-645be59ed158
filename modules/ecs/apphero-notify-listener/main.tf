provider "aws" {
  region = var.region
}

# Create ecr repository
resource "aws_ecr_repository" "apphero_notify_listener" {
  name = "apphero-notify-listener-${var.environment}"
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}

resource "aws_security_group" "apphero_notify_ecs_sg" {
  name        = "apphero-notify-ecs-${var.environment}-sg"
  description = "Application sg for apphero-notify-listener"
  vpc_id      = var.aws_vpc[var.environment]
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }

  ingress {
    description = "Allow inbound traffic on port 3003"
    from_port   = 3003
    to_port     = 3003
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Open to all IPv4 addresses, adjust as necessary
  }

  # Define egress rules (outbound traffic)
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"  # Allow all protocols
    cidr_blocks = ["0.0.0.0/0"]  # Allow all outbound traffic
  }
}

resource "aws_ecr_lifecycle_policy" "apphero_notify_listener_lifecycle_policy" {
  repository = aws_ecr_repository.apphero_notify_listener.name

  policy = jsonencode({
    rules = [
      {
        rulePriority    = 1,
        description     = "in prod 10 images and in dev 3 images ",
        selection       = {
          tagStatus = "any",
          countType = "imageCountMoreThan",
          countNumber = var.environment == "prod" ? 10 : 3  # Adjust retention period as needed
        },
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# IAM Role for ECS Task
resource "aws_iam_role" "apphero_notify_listener" {
  name = "apphero-notify-listener-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = {
          Service = ["ecs-tasks.amazonaws.com"]
        }
        Action    = "sts:AssumeRole"
      }
          ]
  })
}

# IAM Policy for ECS Task
resource "aws_iam_policy" "apphero_notify_listener" {
  name = "apphero-notify-listener-policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        # allow scheduler to execute the task
        Effect = "Allow",
        Action = [
          "ecs:RunTask"
        ]
        # trim :<revision> from arn, to point at the whole task definition and not just one revision
        Resource = [trimsuffix(aws_ecs_task_definition.task.arn, ":${aws_ecs_task_definition.task.revision}")]
      },
      {
          "Effect": "Allow",
          "Action": "ecr:GetAuthorizationToken",
          "Resource": "*"
      },
      { # allow apphero-notify-listener to set the IAM roles of your task
        Effect = "Allow",
        Action = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams",
          "dynamodb:GetItem",
          "dynamodb:UpdateItem"
        ]
        Resource = [
          "arn:aws:ecr:${var.region}:${var.accountId}:repository/apphero-notify-listener-${var.environment}",
          "arn:aws:ecr:${var.region}:${var.accountId}:repository/apphero-notify-listener-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/ecs/apphero-notify-listener-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:apphero-notify-listener-${var.environment}:log-stream:*",
          "arn:aws:dynamodb:${var.region}:${var.accountId}:table/apphero-events-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:apphero-logs-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:apphero-logs-${var.environment}:log-stream:*"
          ]
      },
      ]
  })
}

resource "aws_iam_role_policy_attachment" "apphero-notify_policy_attachment" {
  role       = aws_iam_role.apphero_notify_listener.name
  policy_arn = aws_iam_policy.apphero_notify_listener.arn
}

# Use local variables to reference the repository URI
locals {
  ecr_repo_uri = aws_ecr_repository.apphero_notify_listener.repository_url
  image_with_tag = "${local.ecr_repo_uri}:latest"
}

resource "aws_ecs_task_definition" "task" {
  requires_compatibilities = ["FARGATE"]
  cpu                      = 256
  memory                   = 512
  skip_destroy             = true
  network_mode             = "awsvpc"
  # role that allows ECS to spin up your task, for example needs permission to ECR to get container image
  execution_role_arn = aws_iam_role.apphero_notify_listener.arn
  # role that your workload gets to access AWS APIs
  task_role_arn      = aws_iam_role.apphero_notify_listener.arn

  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = "X86_64"
  }

  family = "apphero-notify-listener-${var.environment}"
  container_definitions = jsonencode([
    {
      name         = "apphero-notify-listener-${var.environment}"
      image        = local.image_with_tag 
      cpu          = 256
      memory       = 512
      essential    = true
      requires_compatibilities = ["FARGATE"]
      network_mode             = "awsvpc"
      portMappings = [
        {
          containerPort = 3003
          hostPort      = 3003
          protocol      = "tcp"
        }
      ]
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = aws_cloudwatch_log_group.log_group.name
          awslogs-region        = "${var.region}"
          awslogs-stream-prefix = "apphero-notify-listener"
        }
      }
    }
  ])
}

resource "aws_cloudwatch_log_group" "log_group" {
  name              = "apphero-notify-listener-${var.environment}"  # Name of the log group
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }  

}

resource "aws_ecs_service" "apphero_notify_listener_service" {
  name            = "apphero-notify-listener-${var.environment}"
  cluster         = "apphero-cluster-${var.environment}"
  task_definition = aws_ecs_task_definition.task.arn
  desired_count   = 1
  launch_type     = "FARGATE"
  network_configuration {
    subnets = var.private_subnet_id[var.environment]
    security_groups = [aws_security_group.apphero_notify_ecs_sg.id]
    assign_public_ip = false
  }
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}


