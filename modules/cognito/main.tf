
resource "aws_cognito_user_pool" "user_pool" {
  name = "${var.environment}-apphero"
  username_attributes = ["email"]
  email_configuration {
    source_arn = var.ses_mailer_arn
    email_sending_account = "DEVELOPER"
  }
  schema {
    name = "email"
    attribute_data_type = "String"
    required = true
    mutable = true
    string_attribute_constraints {
      min_length = 8
      max_length = 128
    }
  }

  schema {
    name = "phone_number"
    attribute_data_type = "String"
    required = false
    mutable = true
    string_attribute_constraints {
      min_length = 10
      max_length = 15
    }
  }

  schema {
    name = "userId"
    attribute_data_type = "String"
    mutable = true
    string_attribute_constraints {
      min_length = 6
      max_length = 32
    }
  }

  schema {
    name = "first_name"
    attribute_data_type = "String"
    mutable = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name = "last_name"
    attribute_data_type = "String"
    mutable = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name = "country"
    attribute_data_type = "String"
    mutable = true
    string_attribute_constraints {
      min_length = 1
      max_length = 70
    }
  }

  schema {
    name = "country_code"
    attribute_data_type = "String"
    mutable = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name = "mobile_number"
    attribute_data_type = "String"
    mutable = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  password_policy {
    minimum_length = 6
    require_lowercase = false
    require_numbers = true
    require_uppercase = true
    require_symbols = true
  }
  auto_verified_attributes = ["email"]  
  lambda_config {
    pre_sign_up  = "arn:aws:lambda:${var.region}:${var.accountId}:function:apphero-preSignUpTrigger-${var.environment}"
    custom_message = "arn:aws:lambda:${var.region}:${var.accountId}:function:apphero-customEmailTrigger-${var.environment}"
    post_confirmation = "arn:aws:lambda:${var.region}:${var.accountId}:function:apphero-postSignUpTrigger-${var.environment}"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS Cognito User Pool Identity Provider
resource "aws_cognito_identity_provider" "user_pool_identity_provider" {
  provider_name = "linkedin"
  provider_type = "OIDC"
  user_pool_id = aws_cognito_user_pool.user_pool.id

  provider_details = {
    client_id                 = var.cognito_linkedin_identity_provider_client_id
    client_secret             = var.cognito_linkedin_identity_provider_client_secret
    authorize_scopes          = var.cognito_linkedin_identity_provider_authorize_scopes
    oidc_issuer               = var.cognito_linkedin_identity_provider_oidc_issuer
    attributes_request_method = var.cognito_linkedin_identity_provider_attributes_request_method
    authorize_url             = "https://www.linkedin.com/oauth/v2/authorization"
    token_url                 = "https://www.linkedin.com/oauth/v2/accessToken"
    attributes_url            = "https://api.linkedin.com/v2/userinfo"
    jwks_uri                  = "https://www.linkedin.com/oauth/openid/jwks"
  }

  attribute_mapping = {
    "custom:first_name" = "given_name"
    "custom:last_name"  = "family_name"
    "email"             = "email"
    "email_verified"    = "email_verified"
    "picture"           = "picture"
    "username"          = "sub"
  }
}

# Define the AWS Cognito User Pool Client
resource "aws_cognito_user_pool_client" "user_client" {
  name = "${var.environment}-apphero-client"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  generate_secret = false
  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_flows = var.cognito_user_pool_client_allowed_oauth_flows
  allowed_oauth_scopes = var.cognito_user_pool_client_allowed_oauth_scopes
  
  explicit_auth_flows = var.cognito_user_pool_client_explicit_auth_flows
  
  callback_urls = var.cognito_user_pool_client_callback_urls
  logout_urls = var.cognito_user_pool_client_logout_urls
  
  supported_identity_providers = var.cognito_user_pool_client_supported_identity_providers
  
  read_attributes = ["email", "phone_number", "custom:first_name", "custom:last_name", "custom:userId", "custom:country", "custom:country_code", "custom:mobile_number"]
  write_attributes = ["email", "phone_number", "custom:first_name", "custom:last_name", "custom:userId", "custom:country", "custom:country_code", "custom:mobile_number"]

  access_token_validity     = 1
  id_token_validity         = 1
  refresh_token_validity    = 30
}
resource "aws_cognito_resource_server" "apphero_resource_server" {
  user_pool_id  = aws_cognito_user_pool.user_pool.id
  identifier    = "apphero"
  name          = "AppHero API Resource Server"
  
  scope {
    scope_name        = "read"
    scope_description = "Read access to resources"
  }
}
resource "aws_cognito_user_pool_client" "gisma_student_oap_client" {
  name = "${var.environment}-gisma-student-oap"
  user_pool_id = aws_cognito_user_pool.user_pool.id
  generate_secret = false
  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_flows = var.cognito_user_pool_client_allowed_oauth_flows
  allowed_oauth_scopes = var.cognito_user_pool_client_allowed_oauth_scopes

  explicit_auth_flows = var.cognito_user_pool_client_explicit_auth_flows

  callback_urls = var.cognito_user_pool_client_callback_urls
  logout_urls = var.cognito_user_pool_client_logout_urls

  supported_identity_providers = var.cognito_user_pool_client_supported_identity_providers

  read_attributes = ["email", "phone_number", "custom:first_name", "custom:last_name", "custom:userId", "custom:country", "custom:country_code", "custom:mobile_number"]
  write_attributes = ["email", "phone_number", "custom:first_name", "custom:last_name", "custom:userId", "custom:country", "custom:country_code", "custom:mobile_number"]

  access_token_validity     = 24
  id_token_validity         = 1
  refresh_token_validity    = 30
}

resource "aws_cognito_user_pool_client" "chat_client" {
  name         = "${var.environment}-chat-apphero-client" 
  user_pool_id = aws_cognito_user_pool.user_pool.id
  access_token_validity = 24

  allowed_oauth_flows             = ["client_credentials"]
  allowed_oauth_scopes            = ["apphero/read"]
  allowed_oauth_flows_user_pool_client = true

  generate_secret = true
  depends_on = [
    aws_cognito_resource_server.apphero_resource_server
  ]
}


# Define the AWS Cognito Identity Pool
resource "aws_cognito_identity_pool" "identity_pool" {
  identity_pool_name = "${var.environment}-apphero-identityPool"
  allow_unauthenticated_identities = false
  cognito_identity_providers {
    client_id = aws_cognito_user_pool_client.user_client.id
    provider_name = aws_cognito_user_pool.user_pool.endpoint
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS IAM Role for Cognito Identity Pool Auth
resource "aws_iam_role" "authenticated_role" {
  name = "${var.environment}-cognito-apphero-identitypoolAuth-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "cognito-identity.amazonaws.com"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "cognito-identity.amazonaws.com:aud" = aws_cognito_identity_pool.identity_pool.id
          },
          "ForAnyValue:StringLike" = {
            "cognito-identity.amazonaws.com:amr" = "authenticated"
          }
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "authenticated_role_policy" {
  name        = "apphero-authenticated-role-policy-${var.environment}"
  description = "A policy that allows access to s3"
  
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        "Action": "*",
        "Resource": "*",
        "Effect": "Allow"
		  }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "authenticated_policy_attachment" {
  policy_arn = aws_iam_policy.authenticated_role_policy.arn
  role       = aws_iam_role.authenticated_role.name
}
# Attach the IAM Role to the Cognito Identity Pool
resource "aws_cognito_identity_pool_roles_attachment" "identity_pool_role_attachment" {
  identity_pool_id = aws_cognito_identity_pool.identity_pool.id
  roles = {
    authenticated = aws_iam_role.authenticated_role.arn
  }
}

# Define the Cognito User Pool Domain
resource "aws_cognito_user_pool_domain" "user_pool_domain" {
  domain = "apphero-${var.environment}"
  user_pool_id = aws_cognito_user_pool.user_pool.id
}

resource "aws_cognito_user_pool_domain" "custom_apphero_domain" {
  domain       = var.cognito_apphero_custom_domain
  user_pool_id = aws_cognito_user_pool.user_pool.id
  certificate_arn = var.api_gateway_certificate_acm_certificate_arn
}

