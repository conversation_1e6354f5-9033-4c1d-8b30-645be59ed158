# Create a log group for the CodeBuild project apphero-backend
resource "aws_cloudwatch_log_group" "apphero_backend_logs" {
  name = "build-logs-apphero-backend-service-${var.environment}"

  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

//apphero-backend
resource "aws_codebuild_project" "apphero_backend_project" {
  name          = "apphero-backend-service-${var.environment}"
  description   = ""
  build_timeout = "5"
  service_role  = "arn:aws:iam::${var.accountId}:role/apphero-codebuild-access-${var.environment}"

  source {
    type = "CODEPIPELINE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_MEDIUM"
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.apphero_backend_logs.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = "CODEPIPELINE"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}


//gus-apphero
resource "aws_cloudwatch_log_group" "gus_apphero_project" {
  name = "build-logs-gus-apphero-project-${var.environment}"

  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "gus_apphero_project" {
  name          = "${var.gus_apphero_frontend_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.gus_apphero_frontend_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/apphero-codebuild-access-${var.environment}"

  source {
    type = var.gus_apphero_frontend_project_source_type
  }

  environment {
    compute_type                = var.gus_apphero_frontend_project_environment_compute_type
    image                       = var.gus_apphero_frontend_project_environment_image
    type                        = var.gus_apphero_frontend_project_environment_type
    image_pull_credentials_type = var.gus_apphero_frontend_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.gus_apphero_project.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.gus_apphero_frontend_project_artifact_type
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

//gus-student-detail-oaf
resource "aws_cloudwatch_log_group" "gus_student_detail_oaf" {
  name = "build-logs-gus-student-detail-oaf-${var.environment}"

  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "gus_student_detail_oaf_project" {
  name          = "${var.gus_student_detail_oaf_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.gus_student_detail_oaf_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/apphero-codebuild-access-${var.environment}"

  source {
    type = var.gus_student_detail_oaf_project_source_type
  }

  environment {
    compute_type                = var.gus_student_detail_oaf_project_environment_compute_type
    image                       = var.gus_student_detail_oaf_project_environment_image
    type                        = var.gus_student_detail_oaf_project_environment_type
    image_pull_credentials_type = var.gus_student_detail_oaf_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.gus_student_detail_oaf.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.gus_student_detail_oaf_project_artifact_type
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

//gus-student-detail-backend-service
resource "aws_cloudwatch_log_group" "gus_student_detail_backend_project" {
  name = "build-logs-gus-student-detail-backend-${var.environment}"

  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "gus_student_detail_backend_project" {
  name          = "${var.gus_student_detail_backend_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.gus_student_detail_backend_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/apphero-codebuild-access-${var.environment}"

  source {
    type = var.gus_student_detail_backend_project_source_type
  }

  environment {
    compute_type                = var.gus_student_detail_backend_project_environment_compute_type
    image                       = var.gus_student_detail_backend_project_environment_image
    type                        = var.gus_student_detail_backend_project_environment_type
    image_pull_credentials_type = var.gus_student_detail_backend_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.gus_student_detail_backend_project.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.gus_student_detail_backend_project_artifact_type
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}


//apphero-sf-sync-service

resource "aws_cloudwatch_log_group" "apphero-sf-sync-service-project" {
  name = "apphero-sf-sync-service-project-${var.environment}"

  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "apphero-sf-sync-service-project" {
  name          = "${var.apphero_sf_sync_service_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.apphero_sf_sync_service_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/apphero-codebuild-access-${var.environment}"

  source {
    type = var.apphero_sf_sync_service_project_source_type
  }

  environment {
    compute_type                = var.apphero_sf_sync_service_project_environment_compute_type
    image                       = var.apphero_sf_sync_service_project_environment_image
    type                        = var.apphero_sf_sync_service_project_environment_type
    image_pull_credentials_type = var.apphero_sf_sync_service_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.apphero-sf-sync-service-project.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.apphero_sf_sync_service_project_artifact_type
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}