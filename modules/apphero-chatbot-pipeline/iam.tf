resource "aws_iam_role" "codebuild_role" {
  name = "codebuild-${var.brand}-${var.environment}-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "codebuild.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "codebuild_policy" {
  name = "codebuild-${var.brand}-${var.environment}-policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = ["s3:GetObject", "s3:PutObject", "s3:CreateBucket", "s3:PutBucketTagging", "s3:PutEncryptionConfiguration", "s3:PutBucketPolicy",
        "s3:ListBucket", "cloudformation:ValidateTemplate"]
        Resource = [
          "*"
        ]
      },
      {
        "Action" : [
          "dynamodb:*"
        ],
        "Effect" : "Allow",
        "Resource" : [
          "arn:aws:dynamodb:eu-west-1:${var.accountId}:table/apphero-support-cases-${var.environment}"
        ]
      },
      {
        "Action" : [
          "lambda:*"
        ],
        "Effect" : "Allow",
        "Resource" : [
          "arn:aws:lambda:eu-west-1:${var.accountId}:function:apphero-chatbot-case-handler-${var.environment}",
          "arn:aws:lambda:eu-west-1:${var.accountId}:event-source-mapping:*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "cloudformation:*",
          "iam:PassRole"
        ]
        Resource = [
          "arn:aws:cloudformation:eu-west-1:${var.accountId}:stack/apphero-chatbot-case-handler-${var.environment}/*",
          "arn:aws:iam::${var.accountId}:role/apphero-chatbot-case-handler-access-${var.environment}"
        ]
      },
      {
        Effect   = "Allow"
        Action   = ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "logs:DeleteLogGroup","logs:TagResource"]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "codebuild_attachment" {
  role       = aws_iam_role.codebuild_role.name
  policy_arn = aws_iam_policy.codebuild_policy.arn
}

resource "aws_iam_role" "lambda_exec_role" {
  name = "apphero-chatbot-case-handler-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "lambda_policy" {
  name = "lambda-${var.brand}-${var.environment}-policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage",
          "sqs:GetQueueAttributes",
          "sqs:GetQueueUrl"]
        Resource = [
          "*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:*",
          "logs:*",
          "events:*"
          ]
        Resource = [
          "*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_attachment" {
  role       = aws_iam_role.lambda_exec_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}


output "role_arn" {
  value = aws_iam_role.codebuild_role.arn
}
