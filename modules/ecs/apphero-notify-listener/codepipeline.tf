resource "aws_s3_bucket" "apphero_listener_artifact" {
  bucket = "apphero-notify-listener-pipeline-${var.environment}"

  tags = {
    Name        = "apphero-notify-listener-pipeline-${var.environment}"
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}

resource "aws_codepipeline" "apphero_notify_pipeline" {
  name     = "apphero-notify-listener-${var.environment}"
  role_arn = aws_iam_role.pipeline_role.arn
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }

  artifact_store {
    location = "apphero-notify-listener-pipeline-${var.environment}"
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        RepositoryName       = "apphero-notification-events-listener"
        BranchName           = "${var.environment}"
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name            = "BuildAction"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      version         = "1"
      input_artifacts = ["source_output"]
      output_artifacts = ["build_output"]

      configuration = {
        ProjectName = "apphero-notify-listener-${var.environment}"
      }
    }
  }
  # DEPLOY
  stage {
    name = "Deploy"
    action {
      name            = "Deploy"
      category        = "Deploy"
      owner           = "AWS"
      provider        = "ECS"
      version         = "1"
      input_artifacts = ["build_output"]

      configuration = {
        ClusterName = "apphero-cluster-${var.environment}"
        ServiceName = "apphero-notify-listener-${var.environment}"
        FileName    = "imagedefinitions.json"
      }
    }
  }
}

resource "aws_iam_role" "pipeline_role" {
  name = "apphero-notify-listener-pipeline-role-${var.environment}"
  assume_role_policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Principal : {
          Service: "codepipeline.amazonaws.com"
        },
        Action : "sts:AssumeRole"
      }
    ]
  })
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}

resource "aws_iam_policy" "code_pipeline" {
  name        = "apphero-notify-listener-codepipeline-policy-${var.environment}"
  description = "An apphero-notify-listener oap IAM policy"
  policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Action: [
                "codecommit:CancelUploadArchive",
                "codecommit:GetBranch",
                "codecommit:GetCommit",
                "codecommit:GetRepository",
                "codecommit:GetUploadArchiveStatus",
                "codecommit:UploadArchive",
                "codebuild:StartBuild",
                "codebuild:BatchGetBuilds",
                "s3:GetObject",
                "s3:PutObject",
                "s3:PutBucketPolicy",
                "s3:ListBucket",
                "s3:CreateBucket"
            ],
            Resource: [
				        "arn:aws:codecommit:${var.region}:${var.accountId}:apphero-notification-events-listener",
                "arn:aws:codebuild:${var.region}:${var.accountId}:project/apphero-notify-listener-${var.environment}",
				        "arn:aws:s3:::apphero-notify-listener-pipeline-${var.environment}",
				        "arn:aws:s3:::apphero-notify-listener-pipeline-${var.environment}/*"
			]
    }
  ]
})
tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}


resource "aws_iam_role_policy_attachment" "pipeline_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = aws_iam_policy.code_pipeline.arn
}

resource "aws_iam_role_policy_attachment" "pipeline_policy_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSCodePipeline_FullAccess"
}

resource "aws_iam_role_policy_attachment" "ecs_policy_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonECS_FullAccess"
}

resource "aws_iam_role" "apphero_notify_codebuild_role" {
  name = "apphero-notify-listener-codebuild-${var.environment}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "apphero_notify_codebuild_policy" {
  name        = "apphero-notify-listener-codebuild-policy-${var.environment}"
  description = "IAM policy for CodeBuild project"
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "ecr:GetAuthorizationToken",
        "ecr:BatchCheckLayerAvailability",
        "ecr:BatchGetImage",
        "ecr:PutImage",
        "ecr:InitiateLayerUpload",
        "ecr:UploadLayerPart",
        "ecr:CompleteLayerUpload",
        "s3:GetObject",
        "s3:ListBucket",
        "s3:CreateBucket",
        "s3:PutBucketPolicy",
        "s3:PutObject"
        
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "codebuild:CreateReportGroup",
        "codebuild:CreateReport",
        "codebuild:UpdateReport",
        "codebuild:BatchPutTestCases",
        "codebuild:BatchPutCodeCoverages",
        "codebuild:StartBuild",
        "codebuild:BatchGetBuilds"
      ],
      "Resource": "*"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "codebuild_policy_attachment" {
  role      = aws_iam_role.apphero_notify_codebuild_role.name
  policy_arn = aws_iam_policy.apphero_notify_codebuild_policy.arn
}

resource "aws_codebuild_project" "apphero_notify_codebuild" {
  name = "apphero-notify-listener-${var.environment}"
  description = "apphero-notify-listener CodeBuild project created with Terraform"
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
  service_role  = aws_iam_role.apphero_notify_codebuild_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  # Source configuration
  source {
    type            = "CODEPIPELINE"  # Use CodePipeline as source type
    # Define the buildspec content directly within Terraform
    buildspec = <<EOF
version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      # Install Node.js dependencies using npm or yarn
      - npm install -g pnpm
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin ${var.accountId}.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
      - REPOSITORY_URI=${var.accountId}.dkr.ecr.${var.region}.amazonaws.com/apphero-notify-listener-${var.environment}
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=build-$(echo $CODEBUILD_BUILD_ID | awk -F":" '{print $2}')
  build:
    commands:
      #- echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg NODE_ENV=${var.environment} -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...
      - printf '[{"name":"apphero-notify-listener-${var.environment}","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json
      - cat imagedefinitions.json 
artifacts:
    files: imagedefinitions.json
EOF
  }

  # Environment configuration
  environment {
    compute_type                = "BUILD_GENERAL1_SMALL" # Set compute type
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0" # Set Docker image for environment
    type                        = "LINUX_CONTAINER" # Set environment type
    image_pull_credentials_type = "CODEBUILD" # Specify how CodeBuild obtains image pull credentials
  }
}

# CloudWatch Event Rule to trigger pipeline on CodeCommit changes
resource "aws_cloudwatch_event_rule" "apphero_notify_codecommit_trigger_rule" {
  name = "appHeroNotifyCodecommitTriggerRule-${var.environment}"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:apphero-notification-events-listener"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}

# IAM role for CloudWatch Events to start CodePipeline
resource "aws_iam_role" "apphero_notify_cloudwatch_events_role" {
  name = "apphero-notify-listener-cloudwatch-events-role-${var.environment}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "events.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}

# IAM policy for CloudWatch Events to start CodePipeline
resource "aws_iam_policy" "apphero_notify_cloudwatch_events_policy" {
  name        = "apphero-notify-listener-cloudwatch-events-policy-${var.environment}"
  description = "IAM policy for CloudWatch Events to start CodePipeline"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "codepipeline:StartPipelineExecution"
      ],
      "Resource": "${aws_codepipeline.apphero_notify_pipeline.arn}"
    }
  ]
}
EOF

  tags = {
    STAGE = "${var.environment}"
    PROJECT = "APPHERO"
    TEAM = "GUS EIP Team"
  }
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "apphero_notify_cloudwatch_events_policy_attachment" {
  role       = aws_iam_role.apphero_notify_cloudwatch_events_role.name
  policy_arn = aws_iam_policy.apphero_notify_cloudwatch_events_policy.arn
}

# CloudWatch Event Target to trigger the pipeline
resource "aws_cloudwatch_event_target" "apphero_notify_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.apphero_notify_codecommit_trigger_rule.name
  target_id = "appHeroNotifyCodecommitEventTarget"
  arn       = aws_codepipeline.apphero_notify_pipeline.arn
  role_arn  = aws_iam_role.apphero_notify_cloudwatch_events_role.arn
}