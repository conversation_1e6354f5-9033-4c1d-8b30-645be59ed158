resource "aws_s3_bucket" "artifact_bucket" {
  bucket = "${var.brand}-${var.environment}-artifacts"

  tags = {
    Name = "CodeBuild Artifacts Bucket"
  }
}

resource "aws_cloudwatch_log_group" "codebuild_log_group" {
  name = "/aws/codebuild/${var.brand}-${var.environment}-log-group"
  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7 # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "gus-devops-infra"
    TEAM        = "Devops Team"
  }
}

resource "aws_codebuild_project" "build" {
  name         = "${var.brand}-${var.environment}-codebuild-project"
  service_role = var.iam_role_arn

  artifacts {
    type = "CODEPIPELINE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true
  }


  source {
    type      = "CODEPIPELINE"
    #buildspec = file("${path.module}/../brand-a/buildspec.yml")
  }

  cache {
    type = "NO_CACHE"
  }

  logs_config {
    cloudwatch_logs {
      status      = "ENABLED"
      group_name  = aws_cloudwatch_log_group.codebuild_log_group.name
      stream_name = "{CODEBUILD_BUILD_ID}"
    }
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "gus-devops-infra"
    TEAM        = "Devops Team"
  }
}
